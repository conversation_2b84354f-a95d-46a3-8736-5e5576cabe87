const jwt = require('jsonwebtoken');
const logger = requireSrc('commons/logs');

const Redis = requireSrc('redis');
const redis = new Redis('sessions');

const jwksClient = require('jwks-rsa');

function getKey(header, callback) {
  const client = jwksClient({
    jwksUri: 'https://login.microsoftonline.com/common/discovery/keys'
  });
  client.getSigningKey(header.kid, (err, key) => {
    if (err) 
    {
      logger.error(`Get SigningKey Error: ${err}`);
      return callback(err);
    }
    const signingKey = key.getPublicKey();
    callback(null, signingKey);
  });
}

module.exports = {
  getSessionInfoBy: (req) => {
    const accessToken = ((req.headers.authorization || '').split(/\s/))[1] || null;

    return new Promise((resolve) => {
      jwt.verify(accessToken, getKey, {}, (err, decoded) => {
        if (err) {
          logger.error(`JWT verification failed: ${err.message}`);
          return resolve({
            accessToken: null,
            uuid: null,
            username: null,
            issuedTimestamp: null,
            email: null
          });
        }
        // Read the session info from the token
        const sessionInfo = decoded
        resolve({
          accessToken,
          uuid: sessionInfo.uti || null,
          username: sessionInfo.preferred_username || null,
          issuedTimestamp: sessionInfo.iat || null,
          email: sessionInfo.preferred_username || null
        })
      });
    });
  },
  loadSessionInfoFromDb: async (username) => {
    try {
      let session = await redis.get(username);
      return session ? session : {};
    } catch (error) {
      logger.error(`getSessions ERROR: ${JSON.stringify(error)}`);
    }
  },
  updateSessionInfoToDb: async (username, session) => {
    try {
      await redis.set(username, session);
    } catch (error) {
      logger.error(`updateSessions ERROR: ${JSON.stringify(error)}`);
    }
  },
  logoutSessionInfoFromDb: async (username) => {
    try {
      await redis.set(username, 'logout');
    } catch (error) {
      logger.error(`logoutSessions ERROR: ${JSON.stringify(error)}`);
    }
  }
}
