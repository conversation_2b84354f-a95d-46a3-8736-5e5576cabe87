const { apiGwBasePath } = requireSrc('config');
const { getSessionInfoBy, loadSessionInfoFromDb, updateSessionInfoToDb } = requireSrc('services/session');
const responseHandler = requireSrc('commons/responseHandler');
const { cacheTTL } = requireSrc('config');
const axios = require("axios").default;
const logger = requireSrc('commons/logs');
const timestampAdded = 2100

const ADMIN_PORTAL_API_KEY = process.env.CA_GATEWAY_KEY
const ADMIN_PORTAL_API_SECRET = process.env.CA_GATEWAY_SECRET

const UserRoles = {
  APP_USER: "partnereco_admin_portal_app_user",
  APP_ADMIN: "partnereco_admin_portal_app_admin",
  APP_MANAGER: "partnereco_admin_portal_app_manager",
};
const rolesAdmin = ["po", "ba", "ea", "km"];
const rolesManager = ["pm"];

const managedRoleFromApprovalGroup = (approvalResult) => {


  let role = ""
  if (rolesAdmin.includes(approvalResult.role)) {
    role = UserRoles.APP_ADMIN
  } else if (rolesManager.includes(approvalResult.role)) {
    role = UserRoles.APP_MANAGER
  } else {
    role = UserRoles.APP_USER
  }

  return role
}

const validateUnauthorizedToken = async (req, res, next) => {
  // Step 1: Get userInfo from req.headers.authorization
  const updatingSession = await getSessionInfoBy(req);
  if(!updatingSession.accessToken)
  {
    return responseHandler.returnUnauthorized(res);
  }
  // token expire time should be 30 min after login time.
  // issuedTimestamp (iat) is -5 from login time.
  // so, added 35 min to make it 30 min after login time.
  const tokenExpireTime = updatingSession.issuedTimestamp + timestampAdded;
  const currentTime = Math.floor(Date.now()/1000);
  if (!updatingSession.accessToken) {
    return false
  }

  // Step 2: Validate token expired
  if (currentTime > tokenExpireTime) {
    return false
  }
  // Step 2.1: Validate Token On Redis
  const session = await loadSessionInfoFromDb(updatingSession.uuid)
  if (session === 'logout') {
    return false
  } else if (Object.keys(session).length === 0) {
    await updateSessionInfoToDb(updatingSession.uuid, updatingSession);
  }

  // Step 3: Get and validate the session from session table
  try {

    req.headers.apikey = ADMIN_PORTAL_API_KEY 
    req.headers.apisecret = ADMIN_PORTAL_API_SECRET 
  
    return true
    
  } catch (error) {
    logger.error(`validateUnauthorizedToken ERROR: : ${JSON.stringify(error)}`);
    if (error.response.statusCode >= 500) {
      return responseHandler.returnServerError(res, err);
    }
    return responseHandler.returnUnauthorized(res);
  }
}

const validateUnauthorizedTokenMiddleWare = async (req, res, next) => {
  // Step 1: Get userInfo from req.headers.authorization
  const updatingSession = await getSessionInfoBy(req);
  if(!updatingSession.accessToken)
  {
    return responseHandler.returnUnauthorized(res);
  }
  // token expire time should be 30 min after login time.
  // issuedTimestamp (iat) is -5 from login time.
  // so, added 35 min to make it 30 min after login time.
  const tokenExpireTime = updatingSession.issuedTimestamp + timestampAdded;
  const currentTime = Math.floor(Date.now()/1000);
  if (!updatingSession.accessToken) {
    logger.error("!updatingSession.accessToken")
    return responseHandler.returnUnauthorized(res);
  }

  // Step 2: Validate token expired
  if (currentTime > tokenExpireTime) {
    logger.error("currentTime > tokenExpireTime")
    return responseHandler.returnUnauthorized(res);
  }
  // Step 2.1: Validate Token Logout
  const session = await loadSessionInfoFromDb(updatingSession.uuid)
  if (session === 'logout') {
    logger.error("Access Token is Expired or Logout")
    return responseHandler.returnUnauthorized(res);
  } else if (Object.keys(session).length === 0) {
    updateSessionInfoToDb(updatingSession.uuid, updatingSession);
  }

  // Step 3: Get and validate the session from session table
  try {

    req.headers.apikey = ADMIN_PORTAL_API_KEY 
    req.headers.apisecret =  ADMIN_PORTAL_API_SECRET


    const apisRequiredHeaders = [
      ["PUT", "/partners/v1/portal/tickets/subtasks"],
      ["GET", "/portal/certificate/regenerate"],
      ["PUT", "/portal/certificate/regenerate"]
    ]
    const isApiRequiredHeaders = apisRequiredHeaders.some((api) => {
        return req.method == api[0] && req.originalUrl.includes(api[1])
    })

    if (updatingSession.email) {
      try
      {
        const result = await getApprovalGroup(updatingSession.email)
        const role = managedRoleFromApprovalGroup(result)

        req.headers.userrole = result.role
  
        if(isApiRequiredHeaders)
        {
          req.headers['x-auth-division'] = result.division
          req.headers['x-auth-email'] = result.email
          req.headers['x-auth-given-name'] = result.firstname
          req.headers['x-auth-family-name'] = result.lastname
          req.headers['x-auth-roles'] = role
        }
  
  
        if(role == UserRoles.APP_USER || role == UserRoles.APP_MANAGER)
        {
          const resultUserInfo = await getUserInfo(updatingSession.email)
  
          req.headers.userUuid = resultUserInfo.portalUuid
      
        }
      }
      catch(error)
      {
        console.log(error.response.data || error)
      }
      
    }

    
    next()
  } catch (error) {
    logger.error(`validateUnauthorizedTokenMiddleWare ERROR: ${JSON.stringify(error)}`)
    if (error.response.statusCode >= 500) {
      return responseHandler.returnServerError(res, err);
    }
    return responseHandler.returnUnauthorized(res);
  }
}

const getApprovalGroup =  async (email) => {
  try {
    const config = {
      method: "get",
      url: `${apiGwBasePath}partners/v1/portal/tickets/approvalGroup`,
      params: {
        email
      },
      headers: {
        requestuid: "admin-portal-service-rquid",
        resourceownerid: "admin-portal-service",
        apikey: ADMIN_PORTAL_API_KEY,
        apisecret: ADMIN_PORTAL_API_SECRET
      }
    }
    const { data } = await axios(config)

    return data.data
  } catch (error) {
    logger.error(`/partners/v1/portal/tickets/approvalGroup: ${error}`)
    throw error
  }
}

const getUserInfo =  async (email) => {
  try {
    const myUUIDV4 = generateUUIDv4();

    const config = {
      method: "post",
      url: `${apiGwBasePath}partners/v1/portal/users/userinfo`,
      data: {
        email
      },
      headers: {
        requestuid: myUUIDV4,
        resourceownerid: ADMIN_PORTAL_API_KEY
      }
    }
    const { data } = await axios(config)
    console.log("/partners/v1/portal/users/userinfo: ", data.data[0])

    return data.data[0]
  } catch (error) {
    console.error("/partners/v1/portal/users/userinfo: ", error.response)
    throw error
  }
}


const validateUnauthorizedRequest = async (proxyReq, req, res) => {

  if (req.body && req.headers['content-type'] === 'application/json') {
    for (let field in req.body) {
      if (field.toLowerCase().indexOf('password') > -1) {
        req.body[field] = aes256.encrypt(passwordKey, req.body[field]);
      }
    }

    let bodyData = JSON.stringify(req.body);
    // incase if content-type is application/x-www-form-urlencoded -> we need to change to application/json
    proxyReq.setHeader('Content-Type', 'application/json');
    proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
    // stream the content
    proxyReq.write(bodyData);
  }
  else
  {
    proxyReq.setHeader('apiKey', ADMIN_PORTAL_API_KEY)
    proxyReq.setHeader('apiSecret', ADMIN_PORTAL_API_SECRET)
    if (req.headers["x-auth-division"]) {
      // Step 1: Attach Host Header
      proxyReq.setHeader('x-auth-division', req.headers["x-auth-division"])
      proxyReq.setHeader('x-auth-email', req.headers["x-auth-email"])
      proxyReq.setHeader('x-auth-given-name', req.headers["x-auth-given-name"])
      proxyReq.setHeader('x-auth-family-name', req.headers["x-auth-family-name"])
      proxyReq.setHeader('x-auth-roles', req.headers["x-auth-roles"])
    
    }
  }
  

};

function generateUUIDv4() {
  const template = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';
  return template.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
  });
}

module.exports = {
  proxyOptions: {
    target: apiGwBasePath,
    changeOrigin: true,
    secure: false,
    logLevel: 'debug',
    onError(err, req, res) {
      return responseHandler.returnServerError(res, err);
    },
    onProxyReq: async (proxyReq, req, res) => await validateUnauthorizedRequest(proxyReq, req, res),
    onProxyRes: async (proxyRes, req, res) => {
      proxyRes.headers['Strict-Transport-Security'] = "max-age=31536000; includeSubDomains"
    }
  },
  validateUnauthorizedToken,
  validateUnauthorizedTokenMiddleWare
}
