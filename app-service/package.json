{"name": "scb-partner-frontend-adminportal-service", "version": "1.8.4", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "NODE_ENV=SIT IS_PCI=TRUE REDIS_ENDPOINT=localhost REDIS_PORT=6379 node ."}, "author": "", "license": "ISC", "dependencies": {"@azure/msal-node": "^2.6.4", "axios": "^1.4.0", "cors": "^2.8.5", "deepmerge": "^4.2.2", "express": "^4.18.2", "http-proxy-middleware": "^0.20.0", "jsonwebtoken": "^9.0.1", "microservice-config": "git+ssh://***********************/Partner-Ecosystem/microservice-config-eks.git", "redis": "^3.1.2", "winston": "^3.2.1", "vault-helper": "git+ssh://***********************/Partner-Ecosystem/vault-helper.git", "cookie-parser": "^1.4.6", "jwks-rsa": "^3.2.0"}}