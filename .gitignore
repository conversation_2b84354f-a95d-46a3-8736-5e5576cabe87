# See http://help.github.com/ignore-files/ for more about ignoring files.

# dependencies
/node_modules
/app/node_modules
/app-service/node_modules
/app-service/logs*
/app-service/sessionLogs*
/sessionLogs*
/logs*
.vscode*
/app/.angular

# System Files
.DS_Store
Thumbs.db

# compiled output
/app/dist
/app/tmp
/app/out-tsc
# Only exists if <PERSON><PERSON> was run
/app/bazel-out

# profiling files
/app/chrome-profiler-events*.json
/app/speed-measure-plugin*.json

# IDEs and editors
/app/.idea
/app/.project
/app/.classpath
/app/.c9/
/app/*.launch
/app/.settings/
/app/*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# misc
/app/.sass-cache
/app/connect.lock
/app/coverage
/app/libpeerconnection.log
/app/npm-debug.log
/app/yarn-error.log
/app/testem.log
/app/typings


# package-lock.json
/app-service/package-lock.json

/app/.angular
