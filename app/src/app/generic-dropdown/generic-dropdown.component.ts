import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { GenericDropdownCollection } from '../models/generic-dropdown-collection';
import { UITextForMigratingService } from '../services/uitext/uitext.service';

@Component({
    selector: 'app-generic-dropdown',
    templateUrl: './generic-dropdown.component.html',
    styleUrls: ['./generic-dropdown.component.less'],
    standalone: false
})
export class GenericDropdownComponent implements OnInit {

  public _collection: GenericDropdownCollection;

  @Input('collection')
  public set collection(col: GenericDropdownCollection) {
    this._collection = col;
    this.setCollection();
  }

  public get collection(): GenericDropdownCollection  {
    return this._collection;
  }

  @Input() useLabelBlack: boolean = false;
  @Input() hasError: boolean = false;
  @Input() hasRequiredError: boolean = false;
  @Input() selectedItem = '';
  @Input() assignedLabel = '';
  @Input() placeholderText = 'Please choose';
  @Input() readonly: boolean = false;
  @Input() hiddingErrorMassage: boolean = false;
  @Input() scope = '';

  @Output() touched = new EventEmitter<void>();
  @Output() selected = new EventEmitter<any>();
  @Output() changed = new EventEmitter<any>();

  public dropdownData: any;

  private baseMenuHeight: number = 28;
  public totalMenuHeight: number = 210;

  ngOnInit() {}

  setCollection() {
    const convertedArray = [];
    if (this._collection.collectionType === 'keyValue') {
      for (const key in this._collection.collectionData) {
        if (this._collection.collectionData) {
          convertedArray.push({ key, value: this._collection.collectionData[key] });
        }
      }
    } else if (this._collection.collectionType === 'arrayObjects') {
      const { iKey, iValue, collectionData } = this._collection;
      for (const item of collectionData) {
        convertedArray.push({ key: item[iKey], value: item[iValue] });
      }
    } else if (this._collection.collectionType === 'array') {
      for (const item of this._collection.collectionData) {
        convertedArray.push({ key: item, value: item });
      }
    }
    this.dropdownData = convertedArray;
    this.totalMenuHeight = (this.dropdownData.length * this.baseMenuHeight) + 18;
  }

  constructor(public labels: UITextForMigratingService) {}

  getSelectedItem() {
    if (!this.dropdownData) {
      return '';
    }
    const matched = (this.dropdownData.find(item => item.key === this.selectedItem) || { value: '' });
    return matched.value;
  }

  onChanged(isOpen: boolean) {
    if (!isOpen) {
      this.touched.emit();
      this.changed.emit();
    }
  }

  onItemSelected(item) {
    this.selected.emit({
      item
    });
  }
}
