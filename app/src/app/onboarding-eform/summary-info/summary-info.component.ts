import { CountriesService } from './../../services/countries/countries.service';
import { Component, OnInit, Input, AfterViewInit, ViewChild, OnChanges } from '@angular/core';
import { UITextForMigratingService } from '../../services/uitext/uitext.service';
import numeral from 'numeral';
import { UserForMigratingService } from '../../services/user/user.service';
import { AuthenticationComponent } from '../authentication/authentication.component';
import { DateTime } from "luxon"


const AVAILABLE_PLATFORMS = {
  mobile_app: 'Mobile App',
  mobile_web: 'Mobile Web',
  embedded_web: 'Embedded Web',
  desktop_web: 'Desktop Web',
  server_to_server: 'Server to Server'
};

@Component({
    selector: 'app-summary-info',
    templateUrl: './summary-info.component.html',
    styleUrls: ['./summary-info.component.less'],
    standalone: false
})
export class SummaryInfoComponent implements OnInit, AfterViewInit, OnChanges {

  @Input() generalInfo: any = {};
  @Input() scopeObject: any = {};
  @Input() opts: any = {};
  @Input() certificateValues: any = {};
  @Input() applicationCode: string;

  @ViewChild(AuthenticationComponent, {static: true}) authentication: AuthenticationComponent;
  @Input() authenticationValues: any;

  private autoCert: string = 'AUTO';
  public ownCert: string = 'CSR';

  public _targetEnv: string;
  @Input('targetEnv')
  public set targetEnv(env: string) {
    this._targetEnv = env;
  }

  public get targetEnv(): string {
    return this._targetEnv;
  }

  constructor(
    public labels: UITextForMigratingService,
    private UserForMigratingService: UserForMigratingService,
    private countryService: CountriesService,

  ) {
  }

  isTypeString(value) {
    return typeof value === 'string';
  }

  formatDisplayDate(date: string) {
    const formattedDate = DateTime.fromISO(date).toFormat("dd/LL/yyyy");
    return formattedDate;
  }
  
  // formatDisplayDate(date: string) {
  //   return DateTime.fromISO(date).toFormat("dd/LL/yyyy");
  // }

  getCountryByCode(countryCode: string) {
    return this.countryService.getByCode(countryCode);
  }

  getPlatformByKey(key: string): string {
    return AVAILABLE_PLATFORMS[key] || '(None)';
  }

  getIndustryByCode(code: string) {
    return (this.UserForMigratingService.cachedIndustries.find(industry => industry.industrialCode === parseInt(code, 10)) || { industrialName: '-' }).industrialName;
  }

  formatAmount(amount: string) {
    return this.numberWithCommas(amount)
  }

  numberWithCommas(amount: string) {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }

  showCertificateType() {
    if ( this.certificateValues.certificateType === this.autoCert ) {
      return this.labels.current.ONBOARDING_AUTO_GEN_CSR;
    } else {
      return this.labels.current.ONBOARDING_SUBMIT_OWN_CSR;
    }
  }

  ngOnInit() {
  }

  ngOnChanges() {
    this.authentication.initAuthenticationData(this.authenticationValues);
  }

  ngAfterViewInit() {
    // Auto resize all <textarea> after render view to fit with content.
    const textareas = document.getElementsByTagName('textarea');
    for (let i = 0; i < textareas.length; i++) {
      textareas[i].style.height = textareas[i].scrollHeight - 8 + 'px';
    }
  }
  
  showFileUploadName(testingResultFile) {
    // Reduce and decode full url into filename only.
    let testingResultFileDecoded = decodeURIComponent(testingResultFile.split('/').pop().split('?')[0]);

    return testingResultFileDecoded;
  }
}
