import { Component, OnInit, ViewChild, ElementRef, Input } from '@angular/core';
import { UITextForMigratingService } from '../../services/uitext/uitext.service';
import { BehaviorSubject } from 'rxjs';
import * as uuid from 'uuid';

@Component({
    selector: 'app-certificate-request',
    templateUrl: './certificate-request.component.html',
    styleUrls: ['./certificate-request.component.less'],
    standalone: false
})
export class CertificateRequestComponent implements OnInit {
  @ViewChild('fileInput', { static: true }) fileInput: ElementRef;

  public _action: string ;
  @Input('action')
  public set action(act: string) {
    if (act === 'promote') {
      this.certificateUuid = this.randomUuid;
      this.csrFile = null;
      this.csrName = '';
    }
    this._action = act;
  }
  public get action(): string {
    return this._action;
  }
  @Input() targetEnv: string;
  @Input() isCertError: boolean;
  @Input() isRegenerateCertificate: boolean;
  @Input() disabled: boolean;

  enabledButton: boolean = false;
  isValidFile: boolean = true;
  isValidSize: boolean = true;
  randomUuid: string = uuid.v4();
  isCertificateValid = new BehaviorSubject<boolean>(true);

  private ownCert: string = 'CSR';
  private autoCert: string = 'AUTO';

  // This Variable use for onboarding-form
  public certificateUuid: string = this.randomUuid;
  public certificateType: string = this.autoCert;
  public csrFile: File = null;
  public csrName: string = '';

  constructor(public labels: UITextForMigratingService) { }

  ngOnInit() { }

  restoreCertificateSection(cerPayload) {
      this.certificateUuid = cerPayload.certificateUuid;
      this.certificateType = cerPayload.certificateType;
      this.csrFile = cerPayload.csrFile;
      this.csrName = cerPayload.csrName;
      if (cerPayload.certificateType === this.ownCert) {
        this.enabledButton = true;
      }
  }

  getEnableCsrButton() {
    if (this.certificateType === this.autoCert) {
      this.isCertError = false;
      this.clearCertificateFile();
      this.enabledButton = false;
      this.validateCertificate();
    } else {
      this.enabledButton = true;
      this.isValidFile = true;
      this.isValidSize = true;
      this.validateCertificate();
    }
  }

  clearCertificateFile() {
    this.fileInput.nativeElement.value = '';
    this.csrName = '';
    this.csrFile = null;
    this.isValidFile  = true;
    this.isValidSize  = true;
  }

  onSelectFile() {
    const _self = this;
    const uploadedCsrFile: File = this.fileInput.nativeElement.files[0];
    const reader = new FileReader();
    if (!uploadedCsrFile) {
      return;
    }
    reader.addEventListener('load', (event: any) => {
      this.isValidFile = this.verifyCertificateType(uploadedCsrFile);
      this.isValidSize = this.verifyCertificateSize(uploadedCsrFile);
      if (this.isValidFile && this.isValidSize) {
        _self.csrFile = uploadedCsrFile;
        _self.csrName = this.csrFile.name;
        _self.isCertificateValid.next(true);
      } else {
        _self.csrFile = null;
        _self.csrName = '';
        _self.isCertificateValid.next(false);
       }
    });
    reader.readAsArrayBuffer(uploadedCsrFile);
    this.isCertError = false;
  }

  verifyCertificateType(file: File): boolean {
    const name = file.name;
    const fileExtension = name.split('.');
    if (fileExtension[1] === 'csr') {
      return true;
    } else {
      return false;
    }
  }

  verifyCertificateSize(file: File): boolean {
    const max_size = 10000;
    if (file.size < max_size) {
      return true;
    } else {
      return false;
    }
  }

  validateCertificate() {
    if (this.certificateType === this.autoCert && this.certificateUuid !== '') {
        this.isCertificateValid.next(true);
    } else if ( this.certificateType === this.ownCert && this.certificateUuid !== '' && this.csrFile != null && this.csrName !== '') {
        this.isCertificateValid.next(true);
    } else {
      this.isCertificateValid.next(false);
    }
  }

  showNameFile() {
    if (this.csrName !== '') {
      return this.csrName;
    } else {
      return this.labels.current.ONBOARDING_OWN_CSR_BROWSE;
    }
  }
}
