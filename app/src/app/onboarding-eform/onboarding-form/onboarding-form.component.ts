import { TicketListService } from './../../services/ticket-list/ticket-list.service';
import { LoadingService } from '../../shared/services/loading-service/loading.service';
import { EformDto } from './../../models/onboarding-e-form/eformDto';
import { OnboardingService } from './../../services/onboarding/onboarding.service';
import { Router, ActivatedRoute } from '@angular/router';
import { SummaryComponent } from '../summary/summary.component';
import { Component, ViewChild, OnInit, DoCheck } from '@angular/core';
import { GeneralInfoComponent } from '../general-info/general-info.component';
import { ApiServicesComponent } from '../api-services/api-services.component';
import { AuthenticationComponent } from '../authentication/authentication.component';
import { UITextForMigratingService } from '../../services/uitext/uitext.service';
import { UserForMigratingService } from '../../services/user/user.service';
import * as _ from 'lodash';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { DateTime } from "luxon"
import { PiiService } from "../../services/pii/pii.service";
@Component({
    templateUrl: './onboarding-form.component.html',
    selector: 'app-onboarding-form',
    styleUrls: ['./onboarding-form.component.less'],
    standalone: false
})
export class OnboardingFormComponent implements OnInit, DoCheck {
  // =====================================================
  // Stage controls
  // =====================================================
  public currentStage: number;
  public enabledButton: boolean = false;
  public hideButton: boolean = false;
  public today: string = DateTime.now().toFormat('dd/LL/yyyy');
  // =====================================================
  // Mode & Action controls
  // =====================================================
  public mode: string;
  public action: string = 'init';
  public actionEvent: string;
  public targetEnvironment: string = 'UAT';
  public taskState: string = 'NEW';
  // =====================================================
  // User interface management
  // =====================================================
  public indicatorsLocked: boolean = false;
  public allowedActions: string[] = [];
  // =====================================================
  // Processed data for the actual integration
  // =====================================================
  public generalInfoValue: any;
  private serialisedScopes: string;
  public certificateValues: any;
  public applicationCodeValues: string;
  public isCertError: boolean = false;
  private originalUATPresignedFileFromAPIResponse: string;
  public authenticationValues: any;
  public currentViewerRole: string
  // =====================================================
  // Child registration
  // =====================================================
  @ViewChild(GeneralInfoComponent, { static: true }) generalInfo: GeneralInfoComponent;
  @ViewChild(AuthenticationComponent, { static: true }) authentication: AuthenticationComponent;
  @ViewChild(ApiServicesComponent, { static: true }) apiServices: ApiServicesComponent;
  @ViewChild(SummaryComponent, { static: true }) summary: SummaryComponent;

  modalRef: NgbModalRef;
  rejectTypeList: any[];
  rejectForm: UntypedFormGroup;

  constructor(
    private onboardingService: OnboardingService,
    private UserForMigratingService: UserForMigratingService,
    private modalService: NgbModal,
    private loading: LoadingService,
    private ticketListService: TicketListService,
    private router: Router,
    private route: ActivatedRoute,
    public labels: UITextForMigratingService,
    private fb: UntypedFormBuilder,
    private piiService:PiiService
  )  {
    this.rejectForm = this.fb.group({
      rejectTypeOption: ['', [Validators.required]],
      comment: ['', [
        Validators.required,
        Validators.minLength(5)
      ]]
    })
  }

  ngOnInit() {
    this.mode = this.route.snapshot.data.mode;
    this.configureFor(this.mode);
    this.rejectTypeList = [{
      id: 'rejected_need_more_info',
      value: 'rejected_need_more_info',
      label: 'Reject ( Require more information )',
    }, {
      id: 'rejected_close',
      value: 'rejected_close',
      label: 'Reject ( Close this ticket )',
    }];
  }

  setGeneralInfo(info: any) {
    this.generalInfoValue = info;
  }

  setScopes(scopes: any) {
    this.serialisedScopes = JSON.stringify(scopes);
  }

  setCertificateSection(cerObject) {
    if (cerObject.isRequireCert) {
      this.certificateValues = {
        certificateType: cerObject.certificateType,
        certificateUuid: cerObject.certificateUuid,
        csrFile: cerObject.csrFile,
        csrName: cerObject.certificateType === 'AUTO' ? '' : this.setCertificateName(cerObject.csrName || cerObject.csrFile),
        isRequireCert: cerObject.isRequireCert,
        certificateExpired: cerObject.certificateExpired,
      };
    } else {
      this.certificateValues = {};
    }
  }

  setApplicationCode(applicationCode) {
    this.applicationCodeValues = applicationCode;
  }

  setCertificateName(csrFullName: any): string {
    const path = csrFullName.split('/');
    if (path.length === 1) {
      return csrFullName;
    } else {
      const csrName = path[6].split('?');
      return csrName[0];
    }
  }

  setStage(stageNo: number): void {
    this.currentStage = stageNo;
  }

  // prevTicketSubtaskUuid;
  submitEForm() {
    if (this.action === 'edit') {
      const subtaskUuid = this.route.snapshot.params.subtaskUuid;
      this.updateEform(subtaskUuid);
    } else {
      this.createEform();
    }
  }

  getProdState(subtaskList: any) {
    return subtaskList.find(subtask => subtask.environment === 'PROD' && subtask.subtaskStatus === 'Closed') ? 'EDIT' : 'NEW';
  }

  updateEform(subtaskUuid: string) {
    const authenticationData = this.formatAuthenticationData();
    const eformData = {
      ...this.generalInfoValue,
      scopes: this.serialisedScopes,
      environment: this.targetEnvironment,
      actionEvent: this.actionEvent,
      taskState: this.taskState,
      certificateType: this.certificateValues.certificateType,
      certificateUuid: this.certificateValues.certificateUuid,
      csrFile: this.certificateValues.csrFile,
      applicationCode: this.apiServices.isRequiredAppCode ? this.applicationCodeValues : undefined,
      authenticationType: authenticationData.authenticationType,
    };
    const originalData = sessionStorage.getItem('updateEform');
    // remove unwanted field when user change type of authentication
    if (eformData.authenticationType.includes('2-legged')) {
      eformData.requiresConsent = undefined;
    }

    if (
      eformData.authenticationType.includes('2-legged') ||
      eformData.authenticationType.includes('3-legged') ||
      eformData.authenticationType.includes('hybrid')
    ) {
      eformData.productNameNss = undefined;
      eformData.applicationCodeNss = undefined;
    }

    if (
      eformData.authenticationType.includes('3-legged') ||
      eformData.authenticationType.includes('hybrid') ||
      eformData.authenticationType.includes('web-authen')
    ) {
      eformData.requiresConsent = authenticationData.isRequireConsent;
    }

    if (eformData.authenticationType.includes('web-authen')) {
      eformData.applicationCodeNss = authenticationData.applicationCodeNss;
      eformData.productNameNss = authenticationData.productNameNss;
    }

    const payload: EformDto = this.onboardingService.mapToDTO(eformData);
    if (this.actionEvent === 'rejected_need_more_info' || this.actionEvent === 'rejected_close') {
      const comment = this.rejectForm.get('comment').value;
      payload.comment = comment;
    }
    this.loading.showLoading();
    const postData = {
      parameter: `${payload.corporateTaxId} ${payload.email} ${payload.contactPerson} ${payload.mobileNo}`,
      newValue: `${payload.corporateTaxId} ${payload.email} ${payload.contactPerson} ${payload.mobileNo}`,
      recordKeyValue: '',
      previousValue: originalData
    };
    this.onboardingService.updateSubTask(payload, subtaskUuid).subscribe(response => {
      this.loading.hideLoading();
      if ( response.success) {
        if (this.actionEvent === 'edit_sub_task') {
          this.piiService.postSensitive(postData,'Submit e-Form Request to Checker');
        }
        switch (this.actionEvent) {
          case 'bu_approved':
            this.loading.showSuccess(this.labels.current.EFORM_APPROVED, this.labels.current.EFORM_APPROVED_DESCRIPTION).then(() => {
              this.router.navigate(['request-management']);
            });
            break;
          case 'rejected_need_more_info':
            this.closeModal();
            this.loading.showSuccess(this.labels.current.REJECT_AND_SEND_BACK, this.labels.current.REJECT_AND_SEND_BACK_DESCRIPTION).then(() => {
              this.router.navigate(['request-management']);
            });
            break;
          case 'rejected_close':
            this.closeModal();
            this.loading.showSuccess(this.labels.current.REJECT_AND_CLOSED, this.labels.current.REJECT_AND_CLOSED_DESCRIPTION).then(() => {
              this.router.navigate(['request-management']);
            });
            break;
          default:
            this.loading.showSuccess(this.labels.current.SUCCESS, this.labels.current.ONBOARDING_EFORM_SUBMITTED).then(() => {
              this.router.navigate(['request-management']);
            });
            break;
        }
      } else {
        const isInvalidCsr = response.message === this.labels.current.INVALID_CSR_FILE;
        const msg = isInvalidCsr ? this.labels.current.INVALID_CSR_FILE_ERROR : response.message || this.labels.current.UNEXPECTED_SERVER_ERROR;
        this.loading.showError(this.labels.current.ERROR, msg).then(() => {
          if (isInvalidCsr) {
            this.goToCertSection();
            this.isCertError = true;
          }
        });
      }
    });
  }

  formatAuthenticationData() {
    // format authenticationType
    const authenticationType = [];
    this.apiServices.authentication.authentication.standardAuthentication.forEach(
      auth => {
        if (auth.selected) {
          authenticationType.push(auth.value);
        }
      },
    );
    // format productNameNss
    const productNameNss =
      this.apiServices.authentication.authentication.SMSTemplate.productNameList.map(
        product => product.productName,
      );
    return {
      authenticationType: JSON.stringify(authenticationType),
      applicationCodeNss:
        this.apiServices.authentication.authentication.SMSTemplate
          .applicationCode.value,
      productNameNss: JSON.stringify(productNameNss),
      isRequireConsent:
        this.apiServices.authentication.form.controls.requiresConsent.value,
    };
  }

  createEform() {
    const authenticationData = this.formatAuthenticationData();
    const eformData = {
      ...this.generalInfoValue,
      scopes: this.serialisedScopes,
      environment: this.targetEnvironment,
      taskState: this.taskState,
      certificateType: this.certificateValues.certificateType,
      certificateUuid: this.certificateValues.certificateUuid,
      csrFile: this.certificateValues.csrFile,
      applicationCode: this.apiServices.isRequiredAppCode ? this.applicationCodeValues : undefined,
      authenticationType: authenticationData.authenticationType,
    };

    if (
      eformData.authenticationType.includes('3-legged') ||
      eformData.authenticationType.includes('hybrid') ||
      eformData.authenticationType.includes('web-authen')
    ) {
      eformData.requiresConsent = authenticationData.isRequireConsent;
    }

    if (eformData.authenticationType.includes('web-authen')) {
      eformData.applicationCodeNss = authenticationData.applicationCodeNss;
      eformData.productNameNss = authenticationData.productNameNss;
    }

    const payload: EformDto = this.onboardingService.mapToDTO(eformData);

    if (this.action !== 'init') {
      payload.prevTicketSubtaskUuid = this.generalInfoValue.ticketSubTaskUuid;
    }

    this.loading.showLoading();
    const postData = {
      parameter: `${payload.corporateTaxId} ${payload.email} ${payload.contactPerson} ${payload.mobileNo}`,
      newValue: `${payload.corporateTaxId} ${payload.email} ${payload.contactPerson} ${payload.mobileNo}`,
      recordKeyValue: `${payload.corporateTaxId} ${payload.email} ${payload.contactPerson} ${payload.mobileNo}`,
      previousValue: ''
    };
    this.onboardingService.createSubtask(payload).subscribe(response => {
      this.loading.hideLoading();
      if (response.success) {
        this.piiService.postSensitive(postData,'Submit e-Form Request to Checker');
        this.loading
          .showSuccess(
            this.labels.current.SUCCESS,
            this.labels.current.ONBOARDING_EFORM_SUBMITTED,
          )
          .then(() => {
            this.router.navigate(['request-management']);
          });
      } else {
        const isInvalidCsr =
          response.message === this.labels.current.INVALID_CSR_FILE;
        const msg = isInvalidCsr
          ? this.labels.current.INVALID_CSR_FILE_ERROR
          : response.message || this.labels.current.UNEXPECTED_SERVER_ERROR;
        this.loading.showError(this.labels.current.ERROR, msg).then(() => {
          if (isInvalidCsr) {
            this.goToCertSection();
            this.isCertError = true;
          }
        });
      }
    });
  }

  setAuthSection(authenticationValues): void {
    const mappedData = {
      authenticationType: [],
      isRequireConsent: null,
      applicationCodeNss: '',
      productNameNss: [],
    };
    authenticationValues.authentication.standardAuthentication.forEach(auth => {
      if (auth.selected) {
        mappedData.authenticationType.push(auth.value);
      }
    });
    mappedData.isRequireConsent =
      authenticationValues.form.controls.requiresConsent.value === 'Yes'
        ? '1'
        : authenticationValues.form.controls.requiresConsent.value === 'No'
        ? '0'
        : authenticationValues.form.controls.requiresConsent.value === 'Always'
        ? '2'
        : '';
    mappedData.applicationCodeNss =
      authenticationValues.authentication.SMSTemplate.applicationCode.value;
    mappedData.productNameNss =
      authenticationValues.authentication.SMSTemplate.productNameList.map(
        product => product.productName,
      );
    this.authenticationValues = mappedData;
  }

  goToNext(): void {
    if (this.validateStage() && this.currentStage < 3) {
      if (this.currentStage === 2) {
        this.setGeneralInfo({
          ...this.generalInfoValue,
          ...this.generalInfo.form.value,
        });
        this.setCertificateSection({
          ...this.apiServices.certificateValues,
          isRequireCert: this.apiServices.isRequireCert,
        });
        this.setApplicationCode(
          this.apiServices.applicationCodes.toArray()[0].applicationCode,
        );
        this.setAuthSection(this.apiServices.authentication);
        this.apiServices.serialiseScope(this.authenticationValues);
        this.setScopes(this.apiServices.scopeObject);
      }
      this.currentStage++;
      this.enabledButton = this.validateStage();
    }
  }

  goToStage(stageNo: number) {
    if (this.indicatorsLocked) {
      return false;
    }
    if (this.currentStage > stageNo) {
      this.setStage(stageNo);
      this.enabledButton = this.validateStage();
    }
  }

  goToCertSection() {
    this.setStage(2);
    this.enabledButton = this.validateStage();
    const headers = document.getElementsByClassName('header');
    const certHeader = headers[headers.length - 1];
    certHeader.scrollIntoView();
  }

  extractFilenameFromS3Url(S3Url: string): string {
    const regex = /(\d{10}_)(.*?)(.xlsx|pdf|docx)/g;
    const matched = regex.exec(S3Url);
    return matched ? matched[0] : '';
  }

  confirmOnCancel() {
    if (this.mode === 'manage' && this.action !== 'edit') {
      this.router.navigate(['/request-management']);
    } else {
      this.loading
        .getConfirm(
          this.labels.current.ONBOARDING_EFORM_CANCELATION,
          this.labels.fill(
            this.mode === 'manage' && this.action === 'edit'
              ? this.labels.current.ONBOARDING_EFORM_EDIT_CANCELATION_MESSAGE
              : this.labels.current.ONBOARDING_EFORM_CANCELATION_MESSAGE,
          ),
        )
        .subscribe(isConfirm => {
          if (isConfirm) {
            this.router.navigate(['/request-management']);
          }
        });
    }
  }

  validateStage() {
    if (this.currentStage === 1) {
      return this.generalInfo.form.valid;
    } else if (this.currentStage === 2) {
      return (
        this.apiServices.isValid.getValue() &&
        this.apiServices.certificateValues.isCertificateValid.getValue() &&
        this.apiServices.applicationCodeIsValid.getValue() &&
        this.apiServices.authentication.isValid.getValue()
      );
    } else {
      return true;
    }
  }

  ngDoCheck() {
    if (this.currentStage === 1) {
      this.enabledButton = this.generalInfo.form.valid;
    } else if (this.currentStage === 2) {
      this.enabledButton =
        this.apiServices.isValid.getValue() &&
        this.apiServices.certificateValues.isCertificateValid.getValue() &&
        this.apiServices.authentication.isValid.getValue() &&
        this.apiServices.applicationCodeIsValid.getValue();
    }
  }

  processAllowedActions(subtask: any) {
    const userUuid = this.UserForMigratingService.getUserUuid();
    const createdUserId = subtask.general_section.createdUserId;

    const currUserEmail = this.UserForMigratingService.getSessionInfoBy()
    const createdTicketsByEmail = subtask.general_section.createdByEmail;

    switch (subtask.subTaskStatusDisplayName) {
      case 'Submitted':
        if ((userUuid !== createdUserId) && (currUserEmail.email !== createdTicketsByEmail)) {
          this.allowedActions = ['approve'];
        } else {
          this.allowedActions = ['edit'];
        }
       

        break;
      case 'Pending':
        this.allowedActions = [];
        break;
      case 'Rejected':
        this.allowedActions = [];
        break;
      case 'Follow-up':
        this.allowedActions = ['edit'];
        break;
      case 'Closed': {
        this.allowedActions = [];
        if (subtask.environment === 'UAT') {
          if (subtask.isClonable) {
            this.allowedActions.push('clone');
          }
          if (subtask.isPromotable) {
            this.allowedActions.push('promote');
          }
          
        }
        break;
      }
      default:
        this.allowedActions = [];
        break;
    }
  }

  preprocessGeneralPayload() {
    if (
      this.targetEnvironment === 'PROD' &&
      this.generalInfoValue.uatTestingResultFile ===
        this.originalUATPresignedFileFromAPIResponse
    ) {
      delete this.generalInfoValue.uatTestingResultFile;
    }
  }

  activateEditMode() {
    this.action = 'edit';
    this.actionEvent = 'edit_sub_task';
    this.generalInfo.verifyEmail();
    this.indicatorsLocked = false;
    this.goToStage(1);
  }

  activatePromotionWorkflow() {
    this.loading.showLoading();
    this.ticketListService
      .getTicketByUuid(this.generalInfoValue.ticketUuid)
      .subscribe(resp => {
        this.loading.hideLoading();
        this.taskState = this.getProdState(resp.data.ticketSubtasks);
        this.action = 'promote';
        this.mode = 'add';
        this.targetEnvironment = 'PROD';
        this.generalInfo.verifyEmail();
        this.indicatorsLocked = false;
        this.goToStage(1);
      });
  }

  activateCloningWorkflow() {
    this.action = 'clone';
    this.mode = 'add';
    this.taskState = 'EDIT';
    this.generalInfo.verifyEmail();
    this.indicatorsLocked = false;
    this.goToStage(1);
  }

  configureFor(mode: string) {
    switch (mode) {
      case 'manage':
        this.loading.showLoading();
        this.onboardingService
          .getSubtaskByUuid(this.route.snapshot.params.subtaskUuid)
          .subscribe((response: any) => {
            if (!response.success) {
              this.loading
                .showError(
                  this.labels.current.ERROR,
                  response.message ||
                    this.labels.current.ONBOARDING_UNABLE_TO_LOAD_SUBTASK,
                )
                .then(() => {
                  this.router.navigate(['request-management']);
                });
            } else {
              const resp = response.data;
              sessionStorage.setItem('updateEform', `${resp.corporateTaxId} ${resp.email} ${resp.contactPerson} ${resp.mobileNo}`);
              // Stage setup
              this.currentViewerRole = resp.currentViewerRole
              this.targetEnvironment = resp.environment;
              this.taskState = resp.taskState;
              this.currentStage = 3;
              this.action = 'view';
              this.indicatorsLocked = true;
              // Map API Response to Form Value
              const formValues = this.onboardingService.mapToFormValues(resp);
              // Manipulate form and data
              this.generalInfo.form.patchValue(formValues);
              this.apiServices.setSubtaskScopes(resp.scopes); // Todo: This can be enhanced at the time the reference is loaded
              this.setGeneralInfo({
                ...formValues,
                ticketSubTaskUuid: resp.ticketSubTaskUuid,
                ticketUuid: resp.ticketUuid,
              });

              // Set Certificate Section
              this.setCertificateSection(resp.certificate_section);
              this.apiServices.restoreCertificate(this.certificateValues);

              // Set appCode
              this.applicationCodeValues = formValues.applicationCode || '';
              this.apiServices.applicationCode = this.applicationCodeValues;
              this.apiServices.isRequiredAppCode = this.applicationCodeValues ? true : false;

              // If this is the a PROD environment it contains the UAT testing result. Load it out.
              if (this.targetEnvironment === 'PROD') {
                this.originalUATPresignedFileFromAPIResponse =
                  resp.testingFileFromUAT;
                this.generalInfoValue.uatTestingResultFile =
                  this.extractFilenameFromS3Url(resp.testingFileFromUAT);
                this.generalInfo.fileMessage =
                  this.generalInfoValue.uatTestingResultFile;
                this.generalInfo.isUploadFile = true;
              }

              this.setScopes(resp.scopes);

              // Set Authentication
              this.authenticationValues = {
                authenticationType: JSON.parse(resp.authenticationType),
                applicationCodeNss: resp.applicationCodeNss,
                isRequireConsent: resp.isRequireConsent,
              };

              if (
                this.authenticationValues.authenticationType.includes(
                  'web-authen',
                )
              ) {
                this.authenticationValues.productNameNss = JSON.parse(
                  resp.productNameNss,
                );
              } else {
                this.authenticationValues.productNameNss = [null];
              }

              // Check user who create a ticket and user who want to edit the ticket.
              if ((resp.createdUserId === this.UserForMigratingService.getUserUuid()) ||
              (resp.general_section.createdByEmail === this.UserForMigratingService.getSessionInfoBy().email)
            ) {
                this.hideButton = false;
              } else {
                this.hideButton = true;
              }

              this.apiServices.authentication.initAuthenticationData(
                this.authenticationValues,
              );
              // Process allowed actions from state & status
              this.processAllowedActions(resp);
              // Perform validation
              this.generalInfo.validateOnboardingPeriod();
              this.apiServices.validateService();
              this.loading.hideLoading();
            }
          });
        break;
      default:
        this.currentStage = 1;
        break;
    }
  }

  getCancelOrBackLabel() {
    if (
      this.mode === 'add' ||
      (this.mode === 'manage' && this.action === 'edit')
    ) {
      return this.labels.current.CANCEL;
    }
    return this.labels.current.GOBACK;
  }

  closeModal() {
    this.rejectForm.controls.comment.setValue('');
    this.rejectForm.controls.rejectTypeOption.setValue('');
    this.modalRef.close();
  }

  approve() {
    const subtaskUuid = this.route.snapshot.params.subtaskUuid;
    this.actionEvent = "bu_approved";
    this.updateEform(subtaskUuid);
  }

  reject(content) {
    this.modalRef = this.modalService.open(content, {centered: true, size: 'lg'});
  }

  onConfirmReject() {
    const rejectType = this.rejectForm.get('rejectTypeOption').value;
    const subtaskUuid = this.route.snapshot.params.subtaskUuid;
    this.actionEvent = rejectType;
    this.updateEform(subtaskUuid);
  }

  clearRejectForm() {
    this.modalRef.close();
    this.rejectForm.reset();
  }
}

export interface IStageConfig {
  stageNo: number;
  stageName: string;
  valid: boolean;
}
