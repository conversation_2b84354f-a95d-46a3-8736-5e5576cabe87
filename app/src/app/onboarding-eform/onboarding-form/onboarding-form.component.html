<ng-template #content>
  <div class="modal-body">
    <div style="padding-left: 30px; padding-right: 30px;">
      <div class="modal-sub-title">{{ labels.current.CHOOSE_REJECT_OPTION }}</div>
      <form [formGroup]="rejectForm">
        <div class="form-section">
          <div class="form-section-body">
            <div *ngFor="let rejectType of rejectTypeList; let i = index" class="form-check">
              <input
                class="form-check-input"
                type="radio"
                formControlName="rejectTypeOption"
                id="{{ rejectType.id }}"
                value="{{ rejectType.value }}"
              />
              <label class="form-check-label" for="{{ rejectType.id }}">
                {{ rejectType.label }}
              </label>
            </div>
            <div >
              <label class="form-check-label company-purple">
                {{ labels.current.REASONS_FOR_REJECTION }}
              </label>
              <textarea class="form-control-plaintext" formControlName="comment"></textarea>
              <div class="error-for-reject" >
                <div class="error-comment" *ngIf="rejectForm.controls.comment.touched && rejectForm.controls.comment.hasError('minlength')">
                  {{labels.fill(labels.current.EFORM_REJECT_COMMENT_LENGTH, 5)}}
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
  <div class="modal-footer">
    <div class="row customer-profile-add-modal-buttons">
      <button class="btn btn-lg btn-primary-outline-dark action-button btn-modal" (click)="closeModal()">
        {{ labels.current.CANCEL }}
      </button>
      <button class="btn btn-lg btn-primary action-button w-100 btn-modal" (click)="onConfirmReject()"
        [disabled]="!rejectForm.valid">
        {{ labels.current.SUBMIT }}
      </button>
    </div>
  </div>
</ng-template>

<div class="e-form-panel">
  <div class="triggers">
    <div class="inner-wrapper">
      <div class="section">
        <button type="button" (click)="activatePromotionWorkflow()"
          *ngIf="mode === 'manage' && allowedActions.includes('promote')"
          class="generic-primary-button unleashed"
          data-cy="btn-promote-to-prod">{{labels.current.PROMOTE_TO_PROD}}</button>
        <button type="button" (click)="activateCloningWorkflow()" *ngIf="mode === 'manage' && allowedActions.includes('clone')" class="generic-primary-button" data-cy="btn-renew">Renew</button>
        <button type="button" (click)="approve()" *ngIf="mode === 'manage' && allowedActions.includes('approve')" class="generic-primary-button" data-cy="btn-approve">Approve</button>
        <button type="button" (click)="reject(content)" *ngIf="mode === 'manage' && allowedActions.includes('approve')" class="generic-outline-button-dark" data-cy="btn-reject">Reject</button>
      </div>
      <div class="section text-right">
        <button type="button" class="generic-outline-button-dark" (click)="confirmOnCancel()" data-cy="btn-cancel-or-back">{{ getCancelOrBackLabel() }}</button>
        <button type="button" *ngIf="mode === 'add'" [disabled]="!enabledButton || !generalInfo.onboardingPeriodValid" class="generic-primary-button" (click)="currentStage < 3 ? goToNext() : submitEForm()" data-cy="btn-confirm-or-next">{{currentStage !== 3 ? "Next" : "Confirm"}}</button>
        <button type="button" *ngIf="mode === 'manage' && allowedActions.includes('edit') && !hideButton" [disabled]="action === 'edit' && (!enabledButton || !generalInfo.onboardingPeriodValid)" class="generic-primary-button" (click)="action === 'edit' ? currentStage < 3 ? goToNext() : submitEForm() : activateEditMode()" data-cy="btn-save-or-edit">{{action === 'edit' ? currentStage !== 3 ? "Next" : "Save" : "Edit" }}</button>
      </div>
    </div>
  </div>
  <div class="section-title" id="onboarding-section-title">
    {{
      action === 'promote' ?
      labels.current.PROMOTE_TO_PROD :
      labels.current.ONBOARDING_E_FORM_FOR_PARTNER_ONBOARDING
    }}
  </div>
  <h4 class="e-form-date text-center">Date : {{ today }}</h4>
  <div class="indicator-panel">
    <div class="indicator-wrapper">
      <div class="indicator" [ngClass]="{'active': currentStage >= 1}">
        <div class="indicator-icon">
          <span class="trigger" (click)="goToStage(1)">1</span>
        </div>
        <h4 class="header">Onboarding Information</h4>
      </div>
      <div class="indicator" [ngClass]="{'active': currentStage >= 2}">
        <div class="indicator-icon">
          <span class="trigger" (click)="goToStage(2)">2</span>
        </div>
        <h4 class="header">API Services</h4>
      </div>
      <div class="indicator" [ngClass]="{'active': currentStage >= 3}">
        <div class="indicator-icon">
          <span class="trigger" (click)="goToStage(3)">3</span>
        </div>
        <h4 class="header">Summary</h4>
      </div>
    </div>
  </div>
  <app-general-info
    [hidden]="currentStage !== 1"
    [mode]="mode"
    [action]="action"
    [taskstate]="taskState"
    [targetEnv]="targetEnvironment"
  ></app-general-info>
  <app-api-services [hidden]="currentStage !== 2" [targetEnv]="targetEnvironment" [action]="action" [isCertError]="isCertError" [currentStage]="currentStage"></app-api-services>
  <app-summary *ngIf="currentStage === 3" [generalInfo]="generalInfoValue" [scopeObject]="apiServices.availableScopes" [targetEnv]="targetEnvironment" [certificateValues]="certificateValues" [applicationCode]="applicationCodeValues" authentication="apiServices.authentication" [authenticationValues]="authenticationValues"></app-summary>
</div>
