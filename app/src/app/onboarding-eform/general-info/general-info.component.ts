import { Component, OnInit, Renderer2, ViewChild, ElementRef, Input, OnChanges } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators, ValidatorFn } from '@angular/forms';
import { ValidatorService } from '../../services/validator/validator.service';
import { UITextForMigratingService } from '../../services/uitext/uitext.service';
import { CountriesService } from '../../services/countries/countries.service';
import { NgbDate } from '../../models/ngdate';
import { UserForMigratingService } from '../../services/user/user.service';
import { EmailVerifyService } from '../../services/enrollment-verify/email-verify.service';
import { LoadingService } from '../../shared/services/loading-service/loading.service';
import { BehaviorSubject } from 'rxjs';
import { DateTime } from "luxon"

@Component({
    selector: 'app-general-info',
    templateUrl: './general-info.component.html',
    styleUrls: ['./general-info.component.less'],
    standalone: false
})
export class GeneralInfoComponent implements OnInit, OnChanges {

  form: UntypedFormGroup;
  public hasOnboardingPeriodError: boolean = false;
  public onboardingPeriodValid: boolean = false;
  public countries: string[];
  public industries: any;
  public fileTestingUAT: File;
  MAXIMUM_SIZE: number = 5000000;
  availableType: string[] = ['application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                            'application/pdf', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
  fileValidation: any;
  fileMessage: String = '';
  isUploadFile: Boolean = false;
  emailVerifiedMessage: String = '';
  public _targetEnv: string;
  public contactPositionOptions: any = {
    "Bank Internal": "Bank Internal",
    "Third's party": "Third's party",
    "Project Manager": "Project Manager",
    "Other": "Other",
  };

  @Input() mode: string;
  @Input() action: string;
  @ViewChild('fileInput') fileInput: ElementRef;
  @Input() taskstate: string;

  @Input('targetEnv')
  public set targetEnv(env: string) {
    if (env === 'PROD') {
      this.form.controls.uatTestingResultFile.setValidators([Validators.required]);
      this.form.controls.uatTestingResultFile.updateValueAndValidity();
    }
    this._targetEnv = env;
  }

  public get targetEnv(): string {
    return this._targetEnv;
  }

  @ViewChild('goLiveDateInput', {static: true}) goLiveDateInput: ElementRef;

  constructor(
    public labels: UITextForMigratingService,
    private fb: UntypedFormBuilder,
    private renderer: Renderer2,
    public countryService: CountriesService,
    private validator: ValidatorService,
    private loading: LoadingService,
    private userFUserForMigratingService: UserForMigratingService,
    private emailService: EmailVerifyService,
  ) {
    this.form = this.fb.group({
      organizationName: ['', [
        Validators.required,
        this.validator.onlyAlphanumericalEng,
        Validators.maxLength(45)
      ]],
      organisationEngName: ['', [
        Validators.required,
        this.validator.isValidFreeTextFormat,
        this.validator.isValidSpace,
        Validators.maxLength(45)
      ]],
      organisationThaiName: ['', [
        Validators.required,
        this.validator.isValidFreeTextFormat,
        this.validator.isValidSpace,
        Validators.maxLength(45)
      ]],
      applicationName: ['', [
        Validators.required,
        this.validator.isValidAppName,
        this.validator.isValidSpace,
        Validators.maxLength(50)
      ]],
      applicationEngDisplayName: ['', [
        Validators.required,
        this.validator.isValidFreeTextWithAllowApostrophe,
        this.validator.isValidSpace,
        Validators.maxLength(255)
      ]],
      applicationThaiDisplayName: ['', [
        Validators.required,
        this.validator.isValidFreeTextWithAllowApostrophe,
        this.validator.isValidSpace,
        Validators.maxLength(255)
      ]],
      taxId: ['', [
        Validators.required,
        this.validator.isNumberOnly,
        Validators.minLength(13),
        Validators.maxLength(13)
      ]],
      industrialCode: ['', [
        Validators.required
      ]],
      estimationVolume: ['', [
        Validators.required,
        this.validator.isNumberOnly,
        Validators.maxLength(20)
      ]],
      contactPerson: ['', [
        Validators.required,
        Validators.maxLength(255),
        this.validator.isValidAlphabetCharacter
      ]],
      contactPosition: ['', [
        Validators.required
      ]],
      email: ['', [
        Validators.required,
        this.validator.isEformEmailValid,
        Validators.maxLength(255)
      ]],
      isEmailEnrolled: [null, [
        Validators.requiredTrue
      ]],
      mobileNo: ['', [
        Validators.required,
        this.validator.isNumberOnly,
        this.validator.isLeadingZeroMobileNumberValid,
        Validators.minLength(10),
        Validators.maxLength(10)
      ]],
      devPeriodStart: ['', [
        Validators.required
      ]],
      devPeriodEnd: ['', [
        Validators.required
      ]],
      testPeriodStart: ['', [
        Validators.required
      ]],
      testPeriodEnd: ['', [
        Validators.required
      ]],
      goLiveDate: ['', [
        Validators.required
      ]],
      applicationURL: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      applicationType: ['', [
        Validators.required
      ]],
      serverLocation: ['', [
        Validators.required
      ]],
      uatTestingResultFile: ['', []]
    });

  this.fileValidation = {
    validSize: false,
    validFileType: false
  };


  }

  ngOnInit() {
    this.userFUserForMigratingService.getIndustriesList().subscribe(
      response => {
        if (response.success && response.data) {
          this.industries = response.data;
        } else {
          this.loading.showError(response.title, response.message);
        }
      }
    );
  }

  ngAfterViewChecked() {
    //Add data-cy attribute to <app-generic-dropdown>
    for (let i = 0;i < document.getElementsByTagName('app-generic-dropdown').length;i++) {
      let dropdownCollection = document.getElementsByTagName('app-generic-dropdown',);
      let buttonCollection = dropdownCollection[i].getElementsByTagName('button');
      let label = dropdownCollection[i].getElementsByTagName('label')[0];
      for (let j = 0; j < buttonCollection.length; j++) {
        if (buttonCollection[j].className === 'dropdown-item') {
          buttonCollection[j].setAttribute(
            'data-cy',
            'option-' +
            buttonCollection[j].innerText.replace(/(\r\n|\n|\r|\s)/gm, ''),
          );
        }
        if (buttonCollection[j].className === 'btn btn-outline-primary custom-generic-dropdown dropdown-toggle') {
          buttonCollection[j].setAttribute(
            'data-cy',
            'dropdown-' + label.innerText.replace(/(\r\n|\n|\r|\s)/gm, ''),
          );
        }
      }
    }

    //Add data-cy attribute to <app-industry-dropdown>
    for (let i = 0;i < document.getElementsByTagName('app-industry-dropdown').length;i++) {
      let dropdownCollection = document.getElementsByTagName('app-industry-dropdown',);
      let buttonCollection = dropdownCollection[i].getElementsByTagName('button');
      let label = dropdownCollection[i].getElementsByTagName('label')[0];
      for (let j = 0; j < buttonCollection.length; j++) {
        if (buttonCollection[j].className ==='dropdown-item') {
          buttonCollection[j].setAttribute(
            'data-cy',
            'option-' +
            buttonCollection[j].innerText.replace(/(\r\n|\n|\r|\s)/gm, ''),
          );
        }
        if (buttonCollection[j].className === 'btn btn-outline-primary industry dropdown-toggle') {
          buttonCollection[j].setAttribute(
            'data-cy',
            'dropdown-' + label.innerText.replace(/(\r\n|\n|\r|\s)/gm, ''),
          );
        }
      }
    }
  }

  ngOnChanges() {
    this.applicationTypeCheck();
  }

  updatePopupState(isOpen: boolean) {
    if (!isOpen) {
      this.form.controls.goLiveDate.markAsTouched();
    }
  }

  disableForm() {
    this.form.disable();
  }

  validateOnboardingPeriod() {
    const { goLiveDate, devPeriodStart, devPeriodEnd, testPeriodEnd } = this.form.controls;
    
    const goLive = DateTime.fromFormat(goLiveDate.value, "yyyy-L-d");
    const devStart = DateTime.fromISO(devPeriodStart.value);
    const devEnd = DateTime.fromISO(devPeriodEnd.value);
    const testEnd = DateTime.fromISO(testPeriodEnd.value);
    const periodError = (goLive < devStart || !(goLive > devEnd) || !(goLive > testEnd));
    this.hasOnboardingPeriodError = this.mode === 'add' ? (goLiveDate.touched && periodError) : periodError;
    this.onboardingPeriodValid = !this.hasOnboardingPeriodError;
  }

  onDateSelect(date: NgbDate) {
    // Ensure day and month are zero-padded
    const day = ('0' + date.day).slice(-2);
    const month = ('0' + date.month).slice(-2);
    const year = date.year;
  
    // Format for display (DD/MM/YYYY)
    const displayFormatted = `${day}/${month}/${year}`;
    this.renderer.setProperty(
      this.goLiveDateInput.nativeElement,
      'value',
      displayFormatted
    );
  
    // Format for form value (YYYY-MM-DD)
    const formFormatted = `${year}-${month}-${day}`;
    this.form.controls.goLiveDate.markAsTouched();
    this.form.controls.goLiveDate.setValue(formFormatted);
  
    // Validate onboarding period
    this.validateOnboardingPeriod();
  }

  onSelectFile() {
    this.setFileValidation();

    const _self = this;
    const testingUAT: File = this.fileInput.nativeElement.files[0];
    const reader = new FileReader();
    if (!testingUAT) {
      return;
    }

    reader.addEventListener('load', (event: any) => {
      _self.form.controls.uatTestingResultFile.markAsTouched();
      if (testingUAT.size <= this.MAXIMUM_SIZE) {
        this.fileValidation.validSize = true;
      }
      if (this.availableType.includes(testingUAT.type)) {
        this.fileValidation.validFileType = true;
      }
      const isError: boolean =
        this.fileValidation.validSize && this.fileValidation.validFileType ? false : true;
      if (isError) {
        return;
      }
      _self.fileTestingUAT = testingUAT;
      _self.form.controls.uatTestingResultFile.patchValue(testingUAT);
      _self.fileMessage = this.fileTestingUAT.name;
      _self.isUploadFile = true;
    });
    reader.readAsArrayBuffer(testingUAT);
  }

  setFileValidation() {
    this.fileValidation = {
      validSize: false,
      validFileType: false
    };
  }

  onReset() {
    this.isUploadFile = false;
    this.fileTestingUAT = null;
    this.fileMessage = '';
    this.form.controls.uatTestingResultFile.setValue('');
  }

  applicationTypeCheck() {
    if (this.form.controls.applicationType.value === 'server_to_server') {
      this.form.controls.applicationURL.setValue('');
      this.form.controls['applicationURL'].disable();
      this.form.value.applicationURL = null;
    }
    else {
      this.form.controls['applicationURL'].enable();
    }
  }

  onEmailChange(){
    this.form.controls.isEmailEnrolled.setValue(null);
    this.emailVerifiedMessage = null;
  }

  verifyEmail(){
    if(!this.form.controls.email.hasError('required') && !this.form.controls.email.hasError('invalidEmail')){
      this.emailService.verifyEmailStatus(this.form.controls.email.value).subscribe(
        response => {
          if (response.success) {
            this.form.controls.isEmailEnrolled.setValue(true);
            this.form.controls.isEmailEnrolled.markAsTouched();
          } else {
            this.form.controls.isEmailEnrolled.setValue(false);
            this.form.controls.isEmailEnrolled.markAsTouched();
            this.emailVerifiedMessage = response.message;
          }
        }
      );
    }
  }
}
