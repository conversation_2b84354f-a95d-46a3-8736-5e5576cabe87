import { Component, OnInit, Input } from '@angular/core';
import { UITextForMigratingService } from '../../services/uitext/uitext.service';
@Component({
    selector: 'app-summary',
    templateUrl: './summary.component.html',
    styleUrls: ['./summary.component.less'],
    standalone: false
})
export class SummaryComponent implements OnInit {

  @Input() generalInfo: any = {};
  @Input() scopeObject: any = {};
  @Input() opts: any = {};
  @Input() targetEnv: string;
  @Input() certificateValues: any;
  @Input() applicationCode: string;
  @Input() authenticationValues: any;

  constructor(
    public labels: UITextForMigratingService,
  ) { }

  ngOnInit() {
  }
}
