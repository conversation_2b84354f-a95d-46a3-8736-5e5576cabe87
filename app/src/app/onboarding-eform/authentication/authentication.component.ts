import {
  Component,
  OnInit,
  Input,
  <PERSON><PERSON><PERSON>w<PERSON>ni<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@angular/core";
import { BehaviorSubject } from "rxjs";
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { UITextForMigratingService } from "../../services/uitext/uitext.service";
import { ValidatorService } from "../../services/validator/validator.service";
import { EformDto } from "./../../models/onboarding-e-form/eformDto";
import { OnboardingService } from "./../../services/onboarding/onboarding.service";
import { Router, ActivatedRoute } from "@angular/router";
import { LoadingService } from '../../shared/services/loading-service/loading.service';
@Component({
    selector: "app-authentication",
    templateUrl: "./authentication.component.html",
    styleUrls: ["./authentication.component.less"],
    standalone: false
})
export class AuthenticationComponent implements OnInit, AfterV<PERSON>wInit, <PERSON><PERSON><PERSON><PERSON> {
  @Input() applicationCode: string;
  @Input() applicationCodeMismatch: boolean;
  @Input() isDisabled: boolean = false;
  @Input() action: string;
  @Input() mode: string;
  @Input() currentStage: number;

  private validateSpaceRegex = /[ ]{2,}|[\t\r\n]+|^ +| +$/;
  private validateFormatRegex = /^[^"']*$/;
  // public isApplicationCodeEmpty: boolean = false;
  // public isApplicationCodeValidSpace: boolean = false;
  // public isApplicationCodeValidFormat: boolean = false;

  // Check valid of each variable
  public isValid = new BehaviorSubject<boolean>(true);
  // public isValidApplicationCode = new BehaviorSubject<boolean>(true);
  // public isValidProductName = new BehaviorSubject<boolean>(true);

  form: UntypedFormGroup;
  @Input() targetEnv: string;
  authentication: any = {
    isSelected: false,
    standardAuthentication: [
      {
        subScopeId: 1,
        name: "2-Legged Authentication",
        selected: false,
        value: "2-legged",
      }, // 2-legged
      {
        subScopeId: 2,
        name: "3-Legged Authentication (via Easy App)",
        selected: false,
        value: "3-legged",
      },
      {
        subScopeId: 3,
        name: "Hybrid Authentication (2-Legged and 3-legged)",
        selected: false,
        value: "hybrid",
      },
      {
        subScopeId: 4,
        name: "PE web authentication with OTP",
        selected: false,
        value: "web-authen",
      },
    ],
    SMSTemplate: {
      applicationCode: {
        value: "",
        isValidApplicationCode: false,
        isApplicationCodeEmpty: true,
        isApplicationCodeValidSpace: false,
        isApplicationCodeValidFormat: false,
      },
      productNameList: [
        {
          id: 1,
          productName: null,
          isValidProductName: false,
          isProductNameEmpty: true,
          isProductNameValidFormat: false,
          isProductNameValidSpace: false,
        },
      ],
    },
  };

  constructor(
    public labels: UITextForMigratingService,
    private fb: UntypedFormBuilder,
    private onboardingService: OnboardingService,
    private router: Router,
    private route: ActivatedRoute,
    private loading: LoadingService
  ) {
    this.form = this.fb.group({
      requiresConsent: ["", [Validators.required]],
    });
  }

  clearAuthData(): void {
    // web-authen not selected
    if (!this.authentication.standardAuthentication[3].selected) {
      // set appcode = '' and set prodict name = []
      this.authentication.SMSTemplate = {
        applicationCode: {
          value: "",
          isValidApplicationCode: false,
          isApplicationCodeEmpty: true,
          isApplicationCodeValidSpace: false,
          isApplicationCodeValidFormat: false,
        },
        productNameList: [
          {
            id: 1,
            productName: null,
            isValidProductName: false,
            isProductNameEmpty: true,
            isProductNameValidFormat: false,
            isProductNameValidSpace: false,
          },
        ],
      };
      this.applicationCode = "";
    }
    // web-authen && 3-legged && hybrid not selected
    if (
      !this.authentication.standardAuthentication[1].selected &&
      !this.authentication.standardAuthentication[2].selected &&
      !this.authentication.standardAuthentication[3].selected
    ) {
      // set require consent = ''
      this.form.patchValue({
        requiresConsent: "",
      });
    }
  }

  onScopeCheck() {
    this.authentication.isSelected = !this.authentication.isSelected;
    if (this.authentication.isSelected === true) {
      this.authentication.standardAuthentication.forEach((value) => {
        value.selected = true;
      });
    } else {
      this.authentication.standardAuthentication.forEach((value) => {
        value.selected = false;
      });
    }
    this.clearAuthData();
  }

  onSubScopeCheck(subScope: any) {
    subScope.selected = !subScope.selected;
    const isSubScopeSelected = this.authentication.standardAuthentication.some(
      (value) => {
        return value.selected === true;
      }
    );
    if (isSubScopeSelected) {
      this.authentication.isSelected = true;
    } else {
      this.authentication.isSelected = false;
    }
    this.clearAuthData();
  }

  addProductName() {
    const id =
      this.authentication.SMSTemplate.productNameList[
        this.authentication.SMSTemplate.productNameList.length - 1
      ].id + 1;
    const isEveryBoxesHaveProductName =
      this.authentication.SMSTemplate.productNameList.every(
        (item) => item.productName
      );

    // if some boxes don't have productName value, user cannot click + to add productName
    if (!isEveryBoxesHaveProductName) {
      return;
    }
    this.authentication.SMSTemplate.productNameList.push({
      id: id,
      productName: null,
      isProductNameEmpty: true,
      isValidProductName: false,
      isProductNameValidFormat: false,
      isProductNameValidSpace: false,
    });

    // validate new product name after added
    const newProductName =
      this.authentication.SMSTemplate.productNameList[
        this.authentication.SMSTemplate.productNameList.length - 1
      ];
    this.validateProductNameInput(newProductName);
  }

  removeProductName(index) {
    const hasOneProductNameLeft =
      this.authentication.SMSTemplate.productNameList.length === 1;
    if (hasOneProductNameLeft) {
      return;
    }
    this.authentication.SMSTemplate.productNameList.splice(index, 1);
  }

  isDisplayConsentRequired() {
    return this.authentication.standardAuthentication.some((value) => {
      if (value.selected === true && value.subScopeId !== 1) {
        return value.selected === true;
      }
    });
  }

  isDisplaySMSTemplate() {
    return this.authentication.standardAuthentication.some((value) => {
      if (value.selected === true && value.subScopeId === 4) {
        return value.selected === true;
      }
    });
  }

  ngOnInit() {
    // fire api to get data again because cannot pass state from onborading-form.component to this component properly (fix soon)
    // this.onboardingService.getSubtaskByUuid(this.route.snapshot.params.subtaskUuid)
    //   .subscribe((response: any) => {
    //     if (!response.data) return
    //     const authenticationValue = {
    //       authenticationType: JSON.parse(response.data.authenticationType),
    //       applicationCodeNss: response.data.applicationCodeNss,
    //       productNameNss: JSON.parse(response.data.productNameNss),
    //       isRequireConsent: response.data.isRequireConsent
    //     }
    //     this.initAuthenticationData(authenticationValue)
    //   })
  }

  ngAfterViewInit() {
    this.validateApplicationCode();
    this.authentication.SMSTemplate.productNameList.forEach((item) => {
      this.validateProductNameInput(item);
    });
  }

  ngDoCheck() {
    if (
      this.authentication.standardAuthentication.find(
        (auth) => auth.subScopeId === 4 && auth.selected
      )
    ) {
      this.isValid.next(
        this.authentication.isSelected &&
          this.form.valid &&
          this.authentication.SMSTemplate.applicationCode
            .isValidApplicationCode &&
          this.authentication.SMSTemplate.productNameList.every(
            (product) => product.isValidProductName
          )
      );
    } else if (
      this.authentication.standardAuthentication.find(
        (auth) =>
          (auth.subScopeId === 3 || auth.subScopeId === 2) && auth.selected
      )
    ) {
      this.isValid.next(this.authentication.isSelected && this.form.valid);
    } else {
      this.isValid.next(this.authentication.isSelected);
    }
  }

  validateProductNameInput(item) {
    // validate ProductNameInput
    item.isProductNameEmpty = !item.productName;
    item.isProductNameValidSpace = !this.validateSpaceRegex.test(
      item.productName
    );
    item.isProductNameValidFormat = this.validateFormatRegex.test(
      item.productName
    );

    if (
      item.isProductNameEmpty ||
      !item.isProductNameValidSpace ||
      !item.isProductNameValidFormat
    ) {
      item.isValidProductName = false;
    } else {
      item.isValidProductName = true;
    }
  }
  validateApplicationCode() {
    const applicationCode = this.authentication.SMSTemplate.applicationCode;
    applicationCode.isApplicationCodeEmpty = !this.applicationCode;
    applicationCode.isApplicationCodeValidSpace = !this.validateSpaceRegex.test(
      this.applicationCode
    );
    applicationCode.isApplicationCodeValidFormat =
      this.validateFormatRegex.test(this.applicationCode);

    this.authentication.SMSTemplate.applicationCode.value =
      this.applicationCode;

    if (
      applicationCode.isApplicationCodeEmpty ||
      !applicationCode.isApplicationCodeValidSpace ||
      !applicationCode.isApplicationCodeValidFormat
    ) {
      applicationCode.isValidApplicationCode = false;
    } else {
      applicationCode.isValidApplicationCode = true;
    }
  }

  initAuthenticationData(authenticationValue) {
    // logic to update selected authentication on edit mode
    if (authenticationValue) {
      // map isRequireConsent
      if (authenticationValue.isRequireConsent) {
        this.form.patchValue({
          requiresConsent:
            authenticationValue.isRequireConsent === "0"
              ? "No"
              : authenticationValue.isRequireConsent === "1"
              ? "Yes"
              : authenticationValue.isRequireConsent === "2"
              ? "Always"
              : "", // 0 = No, 1 = Yes, 2 = Always
        });
      }

      // map authentication.standardAuthentication
      const selectedAuthenticationType =
        this.authentication.standardAuthentication.filter((item) => {
          return authenticationValue.authenticationType.includes(item.value);
        });
      if (selectedAuthenticationType && selectedAuthenticationType.length) {
        // selectedAuthenticationType.forEach(item => this.onSubScopeCheck(item));
        this.authentication.isSelected = true;
        selectedAuthenticationType.forEach((item) => {
          item.selected = true;
        });
      }

      // map authentication.SMSTemplate.applicationCode
      this.applicationCode = authenticationValue.applicationCodeNss
        ? authenticationValue.applicationCodeNss
        : "";
      this.validateApplicationCode();

      // map authentication.SMSTemplate.productnameList
      const mapProductName = [];
      authenticationValue.productNameNss.forEach((productName, index) => {
        mapProductName[index] = {
          id: index + 1,
          productName: productName,
          isValidProductName: true,
          isProductNameEmpty: true,
          isProductNameValidFormat: true,
          isProductNameValidSpace: true,
        };
        this.validateProductNameInput(mapProductName[index]);
      });
      this.authentication.SMSTemplate.productNameList = mapProductName;
    }
  }
}
