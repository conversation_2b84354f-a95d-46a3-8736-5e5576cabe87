<div class="content-wrappers">
    <div class="topic-header-wrapper">
      <h3 class="header">{{labels.current.ONBOARDING_API_SERVICE_NUMBER}}</h3>
    </div>
    <div class="note-banner" [innerHTML]="labels.current.ONBOARDING_API_SERVICE_NOTE_BANNER"></div>
    <form class="w-100">
      <section [ngClass]="{'disabled': targetEnv === 'PROD'}" >
        <app-authentication [targetEnv]= "targetEnv"[applicationCodeMismatch]="applicationCodeMismatch" [action]="action" [currentStage]="currentStage"></app-authentication>
      </section>
      <div>
        <section *ngFor="let item of availableScopes; let scopeIndex = index" [ngClass]="{'disabled': targetEnv === 'PROD'}">
          <div class="section">
            <div class="scope-half-section">
              <div class="custom-control custom-checkbox">
                  <input type="checkbox" [disabled]="targetEnv === 'PROD'" (click)="onScopeCheck(item.scopeId)" [checked]="item.selected" class="custom-control-input" id="{{item.scopeId}}" *ngIf="item.scopeName">
                  <label class="custom-control-label extra-padding scope-header" for="{{item.scopeId}}">{{item.apiGroupName}}</label>
              </div>
              <p class="scope-description">{{item.apiDescEn}}</p>
              <p class="scope-description" *ngIf="item.note != ''"><b>Note: </b>{{item.note}} </p>
            </div>
            <div class="scope-half-section">
              <div class="sub-scope-wrapper">
                <div class="sub-scope-box" *ngFor="let subGroup of item.groupedSubscopes">
                  <h3 class="sub-scope-header">{{subGroup.groupName}}</h3>
                  <div class="sub-scope-items">
                    <div class="sub-scope-item" *ngFor="let subScope of subGroup.subscopes">
                      <div class="custom-control custom-checkbox">
                        <input [disabled]="targetEnv === 'PROD'" type="checkbox" (click)="onChildScopeCheck(subScope.subScopeId, item.scopeId)" [checked]="subScope.selected" class="custom-control-input" id="{{subScope.subScopeId}}">
                        <label class="custom-control-label extra-padding scope-header" for="{{subScope.subScopeId}}">{{subScope.displayNameEn}}</label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <div class="liner with-y-margin"></div>
      <app-application-code-section [targetEnv]="targetEnv" [isRequiredAppCode]="isRequiredAppCode"  [applicationCode]="applicationCode" [applicationCodeMismatch]="applicationCodeMismatch"  [mode]="'edit'" ></app-application-code-section>
      <app-certificate-request  [disabled]="!isRequireCert" [action]="action" [targetEnv]="targetEnv" [isCertError]="isCertError"> </app-certificate-request>
    </form>
</div>
