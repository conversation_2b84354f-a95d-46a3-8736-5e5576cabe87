import { Component, OnInit, Input, ViewChild, QueryList, ViewChildren, OnChanges, Output, EventEmitter, AfterViewInit, DoCheck } from '@angular/core';
import { ApiScopeService } from '../../services/api-scope/api-scope.service';
import { UITextForMigratingService } from '../../services/uitext/uitext.service';
import { LoadingService } from '../../shared/services/loading-service/loading.service';
import * as _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { CertificateRequestComponent} from '../certificate-request/certificate-request.component';
import { ApplicationCodeSectionComponent } from '../application-code-section/application-code-section.component';
import { AuthenticationComponent } from '../authentication/authentication.component';
@Component({
    selector: 'app-api-services',
    templateUrl: './api-services.component.html',
    styleUrls: ['./api-services.component.less'],
    standalone: false
})
export class ApiServicesComponent implements OnInit, DoCheck {

  availableScopes: any = [];
  scopeObject: any = {};
  isValid = new BehaviorSubject<boolean>(false);
  isRequireCert: boolean = false;
  isRequiredAppCode: boolean = false;
  applicationCode: string;
  subtaskScopes: any;
  applicationCodeMismatch: boolean = false;
  authenticationValue: any;
  @Input() currentStage: number;
  @Input() targetEnv: string;
  @Input() action: string;
  @Input() isCertError: boolean;
  @Output() applicationCodeIsValid = new BehaviorSubject<boolean>(true);
  @ViewChild(CertificateRequestComponent, { static: true }) certificateValues: CertificateRequestComponent;
  @ViewChildren(ApplicationCodeSectionComponent) applicationCodes: QueryList<ApplicationCodeSectionComponent>;
  @ViewChild(AuthenticationComponent, { static: true }) authentication: AuthenticationComponent;

  constructor(
    private scopeService: ApiScopeService,
    public labels: UITextForMigratingService,
    private loading: LoadingService,
  ) { }

  validateService() {
    this.isValid.next(this.availableScopes.filter(scope => scope.selected === true).length > 0);
  }

  serialiseScope(authenticationValues: any): void {
    const scopeArray = ['login'];
    const scopeIdArray = [];
    if (authenticationValues.authenticationType.includes('2-legged')) {
      scopeArray.push('client_credentials');
    }
    if (authenticationValues.authenticationType.includes('hybrid')) {
      scopeArray.push('hybrid');
    }
    if (authenticationValues.isRequireConsent === '0') {
      scopeArray.push('autoconsent');
    }
    if (authenticationValues.isRequireConsent === '2') {
      scopeArray.push('alwaysconsent');
    }

    this.availableScopes.forEach(scope => {
      if (scope.scopeName && scope.selected) {
        scopeArray.push(scope.scopeName);
        scopeIdArray.push(scope.scopeId);
      }
      scope.groupedSubscopes.forEach(group => {
        group.subscopes.forEach(subscope => {
          if (subscope.selected) {
            scopeArray.push(subscope.subScopesName);
            scopeIdArray.push(subscope.subScopeId);
          }
        });
      });
    });
    const deduped = _.uniq(scopeArray);
    this.scopeObject.id = scopeIdArray;
    this.scopeObject.name = deduped;
  }

  restoreScopes(scopePayload) {
    const { id: ids } = scopePayload;
    this.availableScopes.forEach(scope => {
      scope.selected = ids.indexOf(scope.scopeId) !== -1;
      scope.groupedSubscopes.forEach(group => {
        group.subscopes.forEach(subscope => {
          subscope.selected = ids.indexOf(subscope.subScopeId) !== -1;
        });
      });
    });
  }

  restoreCertificate(cerPayload) {
    if (cerPayload.isRequireCert) {
      this.isRequireCert = cerPayload.isRequireCert;
      this.certificateValues.restoreCertificateSection(cerPayload);
    }
  }

  mapSubscopes(data: any) {
    _.each(data, (scope) => {
      scope.selected = false;
      const newData = _.chain(scope.subScopes)
      .groupBy('groupName')
      .map((value, key) => {
        const extendedSubscope = _.map(value, v => ({ ...v, selected: false }));
        return {
          groupName: key,
          subscopes: extendedSubscope,
        };
      })
      .value();
      scope.groupedSubscopes = newData;
    });
    return data;
  }

  processChildCheckboxes(parent: any): void {
    parent.groupedSubscopes.forEach(group => {
      group.subscopes.forEach(subscope => {
        subscope.selected = parent.selected;
      });
    });
  }

  onSetRequiredCert(isRequire) {
    this.isRequireCert = isRequire;
  }

  onSetRequiredAppCode(isRequire) {
    this.isRequiredAppCode = isRequire;
    if (!this.isRequiredAppCode) {
      this.applicationCode = '';
      this.applicationCodes.map(appCode => appCode.resetAppCode());
    }
    // this.applicationCode = this.isRequiredAppCode ? this.applicationCode : '';
  }

  onScopeCheck(scopeId: string): void {
    const targetScope = this.availableScopes.find(scope => scope.scopeId === scopeId);
    targetScope.selected = !targetScope.selected;
    this.processChildCheckboxes(targetScope);
    let requiredCert = false;
    let requiredAppCode = false;
    this.availableScopes.forEach(scope => {
      if (scope.isRequireCert === 1 && scope.selected) {
        requiredCert = true;
      }
      if (scope.isRequireApplicationCode === 1 && scope.selected) {
        requiredAppCode = true;
      }
    });
    this.onSetRequiredCert(requiredCert);
    this.onSetRequiredAppCode(requiredAppCode);
    this.validateService();
  }

  onChildScopeCheck(scopeId: string, parentScopeId: string) {
    const parentScope = this.availableScopes.find(scope => scope.scopeId === parentScopeId);
    const allSubscopes = parentScope.groupedSubscopes.map(group => group.subscopes);
    allSubscopes.forEach(subscope => {
      subscope.forEach(scope => {
        if (scope.subScopeId === scopeId) {
          scope.selected = !scope.selected;
        }
      });
    });
    const selectedChildrenCount = _.flatten(allSubscopes).filter(subscope => subscope.selected === true).length;
    parentScope.selected = selectedChildrenCount > 0;
    let requiredCert = false;
    let requiredAppCode = false;
    this.availableScopes.forEach(scope => {
      if (scope.isRequireCert === 1 && scope.selected) {
        requiredCert = true;
      }
      if (scope.isRequireApplicationCode === 1 && scope.selected) {
        requiredAppCode = true;
      }
    });
    this.onSetRequiredCert(requiredCert);
    this.onSetRequiredAppCode(requiredAppCode);
    this.validateService();
  }

  validateAppCode() {
    this.applicationCodeIsValid.next(this.applicationCodes.toArray().every(code => code.isValid.getValue()));
  }

  setSubtaskScopes (scopes) {
    this.subtaskScopes = scopes;
  }

  ngOnInit() {
    this.scopeService.getAvailableScopes()
    .subscribe((response: any) => {
      if (response.success && response.data) {
        this.availableScopes = this.mapSubscopes(response.data);
      } else {
        this.loading.showError(this.labels.current.ERROR, response.message);
      }
    }, (err) => {
      this.loading.showError(this.labels.current.ERROR, err.message);
    });
  }

  ngDoCheck() {
    if (this.subtaskScopes && this.availableScopes.length > 0) {
      this.restoreScopes(this.subtaskScopes);
      this.validateService();
      this.subtaskScopes = null;
    }
    if (this.isRequiredAppCode && this.currentStage === 2) {
      this.validateAppCode();
    } else {
      this.applicationCodeIsValid.next(true);
    }
    // validate applicationCode of auth section equal to realtime payment
    if (this.isRequiredAppCode && this.authentication.authentication.SMSTemplate.applicationCode.value) {
      this.applicationCodeMismatch =  !this.applicationCodes.toArray().every(code => code.applicationCode === this.authentication.authentication.SMSTemplate.applicationCode.value);
    } else {
      this.applicationCodeMismatch = false;
    }
  }
}
