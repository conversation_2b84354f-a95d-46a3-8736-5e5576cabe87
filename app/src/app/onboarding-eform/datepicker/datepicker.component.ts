import { Component, OnInit, Output, Input, EventEmitter, ViewChild, ElementRef, Renderer2 } from '@angular/core';
import { NgbCalendar, NgbInputDatepicker } from '@ng-bootstrap/ng-bootstrap';
import { NgbDate } from '../../models/ngdate';
import { DateTime } from "luxon"

import { UITextForMigratingService } from '../../services/uitext/uitext.service';
@Component({
    selector: 'app-datepicker',
    templateUrl: './datepicker.component.html',
    styleUrls: ['./datepicker.component.less'],
    standalone: false
})
export class DatepickerComponent implements OnInit {
  displayMonths = 1;
  showWeekNumbers = false;
  autoClose = 'outside';
  outsideDays = 'hidden';
  hoveredDate: NgbDate;

  fromDate: NgbDate;
  toDate: NgbDate;

  @ViewChild('to', { static: true}) dateRangePicker: NgbInputDatepicker;
  @ViewChild('daterangeinput', { static: true}) dateRangeInput: ElementRef;
  @Input() label: string;
  @Input() hasErrors: boolean;
  @Input() hasRequirementError: boolean;
  @Input() dateFrom: string;
  @Input() dateTo: string;
  @Input() maxDateRange: number;
  @Input() disabled: boolean = false;
  @Output() dateRangeSelectionUpdate = new EventEmitter();
  @Output() touched = new EventEmitter();

  constructor(public labels: UITextForMigratingService, private calendar: NgbCalendar, private renderer: Renderer2) {
    this.toDate = calendar.getToday();
    this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 6);

    this.isDateDisabled = this.isDateDisabled.bind(this);
  }

  formatDate(date: NgbDate) {
    return `${date.year}-${('0' + date.month).slice(-2)}-${('0' + date.day).slice(-2)}`;
  }

  updatePopupState(isOpen: boolean) {
    if (!isOpen) {
      this.touched.emit();
    }
  }

  displayInputDate(startDateISO, endDateISO) {
    if (startDateISO === endDateISO) {
      return `Only ${DateTime.fromISO(startDateISO).toFormat('dd LLL yyyy')}`;
    }
    return `${DateTime.fromISO(startDateISO).toFormat('dd/LL/yyyy')} - ${DateTime.fromISO(endDateISO).toFormat('dd/LL/yyyy')}`;
  }

  onDateSelection(date: NgbDate) {
    if (!this.fromDate && !this.toDate) {
      this.fromDate = date;
    } else if (this.fromDate && !this.toDate && !this.isDateBefore(date, this.fromDate)) {
      this.toDate = date;
      this.dateRangePicker.close();
      this.renderer.setProperty(
        this.dateRangeInput.nativeElement,
        'value',
        this.displayInputDate(this.formatDate(this.fromDate), this.formatDate(this.toDate))
      );
      this.dateRangeSelectionUpdate.emit({
        startDate: this.formatDate(this.fromDate),
        endDate: this.formatDate(this.toDate)
      });
    } else {
      this.toDate = null;
      this.fromDate = date;
    }
  }

  isHovered(date: NgbDate) {
    return this.fromDate && !this.toDate && this.hoveredDate && this.isDateAfter(date, this.fromDate) && this.isDateBefore(date, this.hoveredDate);
  }

  isInside(date: NgbDate) {
    return this.isDateAfter(date, this.fromDate) && this.isDateBefore(date, this.toDate);
  }

  isRange(date: NgbDate) {
    return this.isDateEqual(date, this.fromDate) || this.isDateEqual(date, this.toDate) || this.isInside(date) || this.isHovered(date);
  }

  isDateAfter(date1, date2) {
    if (date1 && date2) {
      if (date1.year === date2.year) {
        if (date1.month === date2.month) {
          return date1.day === date2.day ? false : date1.day > date2.day;
        } else {
          return date1.month > date2.month;
        }
      } else {
        return date1.year > date2.year;
      }
    }
    return false;
  }

  isDateBefore(date1, date2) {
    if (date1 && date2) {
      if (date1.year === date2.year) {
        if (date1.month === date2.month) {
          return date1.day === date2.day ? false : date1.day < date2.day;
        } else {
          return date1.month < date2.month;
        }
      } else {
        return date1.year < date2.year;
      }
    }
    return false;
  }

  isDateEqual(date1, date2) {
    if (date1 && date2) {
      return date1.year === date2.year && date1.month === date2.month && date1.day === date2.day;
    }
    return false;
  }

  isDateDisabled(date: NgbDate): Boolean {
    return false;
  }

  ngOnInit() {
    const preselectedDateRange = (this.dateFrom && this.dateTo) ?
      `${DateTime.fromISO(this.dateFrom).toFormat('dd/LL/yyyy')} - ${DateTime.fromISO(this.dateTo).toFormat('dd/LL/yyyy')}` : 'From - To';
    this.renderer.setProperty(
      this.dateRangeInput.nativeElement,
      'value',
      preselectedDateRange
    );
  }
}
