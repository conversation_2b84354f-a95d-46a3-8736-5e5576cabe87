import { Component, Do<PERSON>he<PERSON>, Input, OnInit } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { UITextForMigratingService } from '../../services/uitext/uitext.service';

@Component({
    selector: 'app-application-code-section',
    templateUrl: './application-code-section.component.html',
    styleUrls: ['./application-code-section.component.less'],
    standalone: false
})
export class ApplicationCodeSectionComponent implements OnInit {

  @Input() isRequiredAppCode: boolean;
  @Input() applicationCode: string;
  @Input() mode: string;
  @Input() targetEnv: string;
  @Input() applicationCodeMismatch: boolean;

  private validateSpaceRegex = /[ ]{2,}|[\t\r\n]+|^ +| +$/;
  private validateFormatRegex = /^[^"']*$/;
  // public applicationCode: string = '';
  public isValid = new BehaviorSubject<boolean>(true);
  public isApplicationCodeEmpty: boolean = false;
  public isApplicationCodeInvalidSpace: boolean = false;
  public isApplicationCodeInvalidFormat: boolean = false;
  // public isApplicationCodeLengthInvalid: boolean = false;


  constructor(public labels: UITextForMigratingService) {
  }

  ngOnInit() {
  }

  ngOnChanges(){
    this.validateApplicationCode();
  }

  resetAppCode() {
    this.applicationCode = '';
  }

  validateApplicationCode() {
    // validate applicationCode
    this.isApplicationCodeEmpty = !this.applicationCode;
    this.isApplicationCodeInvalidSpace = this.validateSpaceRegex.test(this.applicationCode);
    this.isApplicationCodeInvalidFormat = !(this.validateFormatRegex.test(this.applicationCode));

    if (this.isApplicationCodeEmpty || this.isApplicationCodeInvalidSpace || this.isApplicationCodeInvalidFormat) {
      this.isValid.next(false);
    } else {
      this.isValid.next(true);
    }
  }
}
