import { Injectable, NgModule } from "@angular/core";
import { Routes, RouterModule, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from "@angular/router";

import { UserRoles } from "./shared/services/user-service/user.service";
import { NotFoundComponent } from "./shared/components/not-found/not-found.component";
import { UserService } from "../../src/app/shared/services/user-service/user.service";
import { Location } from "@angular/common";

// import { MsalRedirectComponent } from "@azure/msal-angular";

const userPath = ["request-management", "request-management/onboarding", "request-management/subtasks", "enrollment", "enrollment/email", "organization", "request-management/certificate", "request-management/certificate/regenerate"]
const adminPath = ["task", "application", "regencert", "user", "apiconsumption/report", "apiconsumption/report/detail","apiInfo/management","apiInfo/management/detail"]
const managerPath = ["tracking", "request-management", "request-management/onboarding", "request-management/subtasks", "enrollment", "enrollment/email", "organization", "request-management/certificate", "request-management/certificate/regenerate", "apiconsumption/report","apiconsumption/report/detail","apiInfo/management","apiInfo/management/detail", "application"]

@Injectable({
  providedIn: 'root',
})
export class UrlGuard  {
  constructor(private router: Router) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    const url = state.url;
    if (url.includes('#')) {
      const newUrl = url.replace(/#/g, ''); // Remove all `#` characters
      const verifyPath = encodeURIComponent(newUrl.split('/')[2]) + '/' + encodeURIComponent(newUrl.split('/')[3]);
      
      if(verifyPath === 'enrollment/email') {
        const verifyToken = encodeURIComponent(newUrl.split('/')[4]);
        this.router.navigateByUrl(verifyPath + '/' + verifyToken);
        return false; // Cancel the current navigation
      } else {
        this.router.navigateByUrl(newUrl);
        return false; // Cancel the current navigation
      }
      
    }
    return true; // Allow navigation
  }
}

@Injectable()
export class PermissionGuard  {
  constructor(private userService: UserService, private router: Router,private location: Location) {
  }
  canActivate = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {

    const path = route.routeConfig.path;
    sessionStorage.removeItem("API_CON_INPUT")
    sessionStorage.removeItem("API_MNG_INPUT")

    if(this.userService.isManager)
    {
      if(!managerPath.includes(path))
      {
        this.router.navigate(['/']);
        return false;
      }
      else
        return true
    }
    else if(this.userService.isAdmin)
    {
      if(!adminPath.includes(path) || (this.userService.userInfo.role === "km" && (path === "apiconsumption/report")))
      {
        this.router.navigate(['/']);
        return false;
      }
      else 
        return true
    }
    else if(this.userService.isUser)
    {
      if(!userPath.includes(path) || this.location.path() === "/request-management/onboarding" || this.location.path() === "/request-management/subtasks")
      {
        this.router.navigate(['/']);
        return false;
      }
      else
        return true
    }
  };
}

const routes: Routes = [
  // { path: "auth/redirect", component: MsalRedirectComponent },
  {
    path: "",
    component: NotFoundComponent,
    pathMatch: "full",
    canActivate: [UrlGuard]
  },
  {
    path: "request-management",
    loadChildren: () => import("./tickets-list/request-management-ticket-list.module").then((m) => m.RequestManagementTicketListModule),
    data: {
      requireAllRoles: true,
      roles: [UserRoles.APP_USER],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "organization",
    loadChildren: () =>
      import("./organization/organization.module").then(
        (m) => m.OrganizationModule
      ),
    data: {
      requireAllRoles: true,
      roles: [UserRoles.APP_USER],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "request-management/onboarding",
    loadChildren: () => import("./onboarding-eform/onboarding-form/onboarding-form.module").then((m) => m.OnboardingFormModule),
    data: {
      requireAllRoles: false,
      roles: [UserRoles.APP_USER],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "request-management/subtasks",
    loadChildren: () => import("./onboarding-eform/onboarding-form/onboarding-form.module").then((m) => m.OnboardingFormModule),
    data: {
      requireAllRoles: true,
      roles: [UserRoles.APP_USER],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "enrollment",
    loadChildren: () =>
      import("./enrollment/enrollment.module").then((m) => m.EnrollmentModule),
    data: {
      requireAllRoles: false,
      roles: [UserRoles.APP_USER],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "enrollment/email",
    loadChildren: () =>
      import("./enrollment-result/enrollment-result.module").then((m) => m.EnrollmentResultModule),
    data: {
      requireAllRoles: false,
      roles: [UserRoles.APP_USER],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "request-management/certificate",
    loadChildren: () => import("./request-management-certificate-list/request-management-certificate-list.module").then((m) => m.RequestManagementCertificateListModule),
    data: {
      requireAllRoles: true
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "task",
    loadChildren: () => import("./task/task.module").then((m) => m.TaskModule),
    data: {
      requireAllRoles: true,
      roles: [UserRoles.APP_ADMIN],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "tracking",
    loadChildren: () =>
      import("./tracking/tracking.module").then((m) => m.TrackingModule),
    data: {
      requireAllRoles: false,
      roles: [UserRoles.APP_MANAGER],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "application",
    loadChildren: () =>
      import("./application/application.module").then(
        (m) => m.ApplicationModule
      ),
    data: {
      requireAllRoles: true,
      roles: [UserRoles.APP_ADMIN],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "regencert",
    loadChildren: () =>
      import("./regencert/regencert.module").then((m) => m.RegenCertModule),
    data: {
      requireAllRoles: true,
      roles: [UserRoles.APP_ADMIN],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "user",
    loadChildren: () => import("./user/user.module").then((m) => m.UserModule),
    data: {
      requireAllRoles: true,
      roles: [UserRoles.APP_ADMIN],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "apiconsumption/report",
    loadChildren: () => import("./api-consumptions/api-consumptions.module").then((m) => m.ApiConsumptionModule),
    data: {
      requireAllRoles: true,
      roles: [UserRoles.APP_ADMIN],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "apiconsumption/report/detail",
    loadChildren: () => import("./api-consumptions/api-consumptions-detail/api-consumptions-detail.module").then((m) => m.ApiConsumptionDetailModule),
    data: {
      requireAllRoles: true,
      roles: [UserRoles.APP_ADMIN],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "apiInfo/management",
    loadChildren: () => import("./api-management/api-management.module").then((m) => m.ApiManagementModule),
    data: {
      requireAllRoles: true,
      roles: [UserRoles.APP_ADMIN],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  {
    path: "apiInfo/management/detail",
    loadChildren: () => import("./api-management/api-management-detail/api-management-detail.module").then((m) => m.ApiManagementDetailModule),
    data: {
      requireAllRoles: true,
      roles: [UserRoles.APP_ADMIN],
    },
    canActivate: [PermissionGuard,UrlGuard]
  },
  { path: "**", redirectTo: "", pathMatch: "full" },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
  providers: [PermissionGuard,UrlGuard]
})
export class AppRoutingModule {}
