import { Component, OnInit } from "@angular/core";
import { LoadingService } from '../shared/services/loading-service/loading.service';
import { UITextForMigratingService } from "../services/uitext/uitext.service";
import { NgForm } from '@angular/forms';
import tableHeadersArray from "./apiconsumptions-table-headers.json";
import dayjs from "dayjs";

import { ApiConsumptionsApiType, ApiConsumptionsReportType, OrgMasterType, AppMasterType, OrgAppMasterType, ConsumptionInputType } from "./model/apiConsumptionsReportType"


import { ApiConsumptionsMasterDataService } from "../../app/services/apiconsumptions-masterdata/apiconsumptions-masterdata.service"
import { ServiceResponse } from '../models/service-response';
import { Router} from '@angular/router';


@Component({
    selector: "app-comsumptions",
    templateUrl: "./api-consumptions.component.html",
    styleUrls: ["./api-consumptions.component.less"],
    standalone: false
})
export class ApiConsumptionComponent implements OnInit {


  // Table Related
  tableHeaders = tableHeadersArray;
  totalRecord = 0;
  pageSize = 20;
  page = 1;
  sizeOptions = [20, 40, 50];
  debounce: any;
  role: string;
  sortBy = "organizationName";
  isAscOrdered = true;
  paginationLabel = "";
  offSet = 0;

  public isMonthSelected:boolean = false

  apiConsumptionsTableSearch: ApiConsumptionsApiType[] = []
  groupedReportsFixed: ApiConsumptionsReportType[] = []
  apiConsumptionsTable: ApiConsumptionsReportType[] = []
  selectedYear: string
  selectedMonth: string
  selectedDay: string
  selectedApp: string
  selectedOrg: string
  selectedPath: string
  dropdownDay: string[] = []
  dropdownMonth: string[] = []
  dropdownYear = [];
  isSubmitted = false
  isTwoxxChecked = true
  isFourxxChecked = true
  isFivexxChecked = true

  keywordToFiltered = ""

  // Master Data
  orgMasterLists: OrgMasterType[] = []
  orgAppMasterLists:OrgAppMasterType[] = []

  appMasterLists: AppMasterType[] = []

  pathMasterLists: {
    apiEndpoint: string
  }[] = []

  // Loading
  isLoadingPath = true
  isLoadingOrgName = true
  isLoadingAppName = true

  constructor(
    public labels: UITextForMigratingService,
    private loading: LoadingService,
    private apiService: ApiConsumptionsMasterDataService,
    private router: Router,
  ) { }

  generateShortMonthNames() {
    const date = dayjs(); // Current date
  
    for (let month = 0; month < 12; month++) {
      this.dropdownMonth.push(date.month(month).format('MMM'));
    }
  
  }

  generateDates() {
    this.dropdownDay = []

    const lastDateOfMonth = dayjs(this.selectedYear+"-"+this.selectedMonth).endOf("month").date()
    for(let i=1 ; i<=lastDateOfMonth ; i++ )
    {
      this.dropdownDay.push(""+i)
    }
  }

  ngOnInit() {

    this.dropdownYear.push(dayjs().format("YYYY"))
    this.dropdownYear.push(dayjs().subtract(1,"year").format("YYYY"))

    this.selectedYear = dayjs().format("YYYY")

    this.generateShortMonthNames()

    this.apiService.getPaths().subscribe((response: ServiceResponse) => {
      this.pathMasterLists = response.data

      this.isLoadingPath = false
    }, (err) => {
      this.loading.showError(this.labels.current.ERROR, err.error ? err.error.status.description : err.message);
    });

    this.apiService.getOrgsApp().subscribe((response:ServiceResponse) => {
      this.orgAppMasterLists = response.data
      this.orgMasterLists = response.data.map((itm: OrgMasterType) => {
        const orgtransformed = {
          organizationId: itm.organizationId,
          organizationName: itm.organizationName
        }
  
        return orgtransformed
      })

      if(sessionApiCon)
      {
        this.isSubmitted = true
  
        const apiConInput:ConsumptionInputType = JSON.parse(sessionApiCon)
  
        this.selectedOrg = apiConInput.orgNameSearch
        this.selectedApp = apiConInput.appNameSearch
        
      }


      this.orgMasterLists =  this.orgMasterLists.filter((item, index, array) => 
        array.findIndex(innerItem => innerItem.organizationId === item.organizationId) === index
      );
      this.orgMasterLists = this.orgMasterLists.sort((a,b) => a.organizationName.localeCompare(b.organizationName) )
      const selectedOrg = {
        organizationId: this.selectedOrg
      }
      if(this.selectedOrg)
        this.appMasterControl(selectedOrg,this.selectedApp)
      else if(this.selectedApp)
        this.appMasterControl(null,this.selectedApp)
      else this.appMasterControl(null,null)


    }, (err) => {
      this.loading.showError(this.labels.current.ERROR, err.error ? err.error.status.description : err.message);

    })

    const sessionApiCon = sessionStorage.getItem("API_CON_INPUT")
    if(sessionApiCon)
    {
      this.isSubmitted = true

      const apiConInput:ConsumptionInputType = JSON.parse(sessionApiCon)
      const pathSearch = apiConInput.pathSearch 
      const orgNameSearch = apiConInput.orgNameSearch
      const appNameSearch = apiConInput.appNameSearch

      this.selectedYear = apiConInput.yearSearch
      this.selectedMonth = apiConInput.monthSearch
      this.selectedDay = apiConInput.daySearch
      this.selectedPath = apiConInput.pathSearch
      this.selectedOrg = apiConInput.orgNameSearch
      this.selectedApp = apiConInput.appNameSearch


      this.retrieveConsumptionReport(pathSearch, orgNameSearch, appNameSearch)
  
      
    }

   



  }

  ngAfterViewChecked() {
  }



  onSubmit(apiConsumptionsForm: NgForm) {
    this.isSubmitted = true
    if (apiConsumptionsForm.invalid) {

      return;
    }

    this.page = 1

    const sessionApiCon = {
      appNameSearch: apiConsumptionsForm.form.controls.appNameSearch.value,
      daySearch: apiConsumptionsForm.form.controls.daySearch.value,
      monthSearch: apiConsumptionsForm.form.controls.monthSearch.value,
      orgNameSearch: apiConsumptionsForm.form.controls.orgNameSearch.value,
      pathSearch: apiConsumptionsForm.form.controls.pathSearch.value,
      yearSearch: apiConsumptionsForm.form.controls.yearSearch.value,
    }

    sessionStorage.setItem("API_CON_INPUT",JSON.stringify(sessionApiCon))


    this.retrieveConsumptionReport(apiConsumptionsForm.form.controls.pathSearch.value, apiConsumptionsForm.form.controls.orgNameSearch.value, apiConsumptionsForm.form.controls.appNameSearch.value)

  }

  retrieveConsumptionReport(pathSearch:string, orgNameSearch:string, appNameSearch:string ) {
    this.loading.showLoading();

    let startDate = ""
    let endDate = ""
    let dateType = ""

    if(!this.selectedMonth && !this.selectedDay)
    {
      dateType = "year"
      startDate = dayjs(`${this.selectedYear}-05-01`).startOf("year").format("YYYY-MM-DD")
      endDate = dayjs(`${this.selectedYear}-05-01`).endOf("year").format("YYYY-MM-DD")
    }
    else if(!(this.selectedDay))
    {
      dateType = "month"
      startDate = dayjs(`${this.selectedYear}-${this.selectedMonth}-01`).startOf("month").format("YYYY-MM-DD")
      endDate = dayjs(`${this.selectedYear}-${this.selectedMonth}-01`).endOf("month").format("YYYY-MM-DD")
    }
    else
    {
      dateType = "day"
      startDate = dayjs(`${this.selectedYear}-${this.selectedMonth}-${this.selectedDay}`).format("YYYY-MM-DD")
      endDate = dayjs(`${this.selectedYear}-${this.selectedMonth}-${this.selectedDay}`).format("YYYY-MM-DD")
    }

    this.offSet = (this.page - 1) * this.pageSize;
    const requestPayload = {
      startDate: startDate,
      endDate: endDate,
      dateType: dateType,
      path: pathSearch ? pathSearch : undefined,
      organizationId: orgNameSearch ? orgNameSearch : undefined,
      applicationId: appNameSearch ? appNameSearch : undefined,
      offSet: this.offSet,
      limit: this.pageSize,
      pageFlag: "list"
    }


    this.apiService.postConsumptionsReport(requestPayload).subscribe((response:ServiceResponse) => {
      this.apiConsumptionsTableSearch = response.data.apiUsage
      this.totalRecord = response.data.total | 0
      this.groupResultsHttpCode()

      this.loading.hideLoading();
      
    }, (err) => {
      this.handleError(err);
    }, () => {
      this.loading.hideLoading();
    })


  }

  onClear(apiConsumptionsForm: NgForm)
  {
    this.isMonthSelected = false
    apiConsumptionsForm.form.reset()
    apiConsumptionsForm.form.controls["yearSearch"].setValue(dayjs().format("YYYY") )

    this.appMasterControl(null,null)
  }

  appMasterControl(orgSelected, appMasterControlSelected) {
    this.selectedApp = appMasterControlSelected
    if(orgSelected)
    {
      this.isLoadingAppName = true
      this.appMasterLists = this.orgAppMasterLists.filter(orgItm => orgItm.organizationId === orgSelected.organizationId )

    
      this.appMasterLists = this.appMasterLists.map((itm: AppMasterType) => {
        const apptransformed = {
          applicationId: itm.applicationId,
          applicationName: itm.applicationName
        }
  
        return apptransformed
      })
      this.appMasterLists = this.appMasterLists.sort((a,b) => a.applicationName.localeCompare(b.applicationName) )
    }
    else
    {
      this.appMasterLists = this.orgAppMasterLists.map((itm: AppMasterType) => {
        const apptransformed = {
          applicationId: itm.applicationId,
          applicationName: itm.applicationName
        }
  
        return apptransformed
      })
      this.appMasterLists = this.appMasterLists .sort((a,b) => a.applicationName.localeCompare(b.applicationName) )
    }

    this.isLoadingOrgName = false
    this.isLoadingAppName = false


  }
  apiConsumptionsShowControl() {
    this.retrieveConsumptionReport(this.selectedPath, this.selectedOrg, this.selectedApp)
  }

  onChangeYear()
  {
    this.selectedDay = null

    if(this.selectedYear && this.selectedMonth)
    {
      this.generateDates()

      this.isMonthSelected = true
    }
    else
    {
      this.isMonthSelected = false
    }
  }

  onChangeMonth()
  {
    this.selectedDay = null
    if(this.selectedMonth && this.selectedYear)
    {
      this.generateDates()
      this.isMonthSelected = true
    }
    else this.isMonthSelected = false
  }

  onHttpCodeClick()
  {
    if(this.isSubmitted)
      this.groupResultsHttpCode()
  }

  generateHeaderSortedId(field) {
    return field
      ? "task-list-header-" +
          field.toLowerCase().split(" ").join("-").replace(".", "")
      : "";
  }

  generatePageSizeOptionId(sizeOption) {
    return sizeOption ? "task-list-display-item-" + sizeOption : "";
  }

  shouldDisabled() {
    return this.totalRecord < this.sizeOptions[0];
  }

  onPageChange() {

    this.apiConsumptionsShowControl()
  }

  selectPageSize(sizeOption) {
    this.pageSize = sizeOption;

    this.apiConsumptionsShowControl()
  }

  genPaginationLabel() {
    let startIndex = this.offSet + 1;
    const lastIndex = this.offSet + this.apiConsumptionsTableSearch.length;

    if (this.apiConsumptionsTable.length === 0) {
      startIndex = 0;
    }
    this.paginationLabel = `Showing ${startIndex}-${lastIndex} of ${this.totalRecord}`;
  }

  filterReport()
  {
    this.apiConsumptionsTable = this.groupedReportsFixed.filter(item => {
      return Object.values(item).some(value =>
        value.toString().toLowerCase().includes(this.keywordToFiltered.toLowerCase())
      );
    });
  }

  groupResultsHttpCode()
  {
    // Group Result Report from above to find remaining deficit 8 hours and fulfill Non-assign to them
    let helperGroupResultReport: any = {} ;

    this.groupedReportsFixed = this.apiConsumptionsTableSearch.reduce((accumulator:ApiConsumptionsReportType[], currentValue:ApiConsumptionsApiType) => {
      var key = `${currentValue.period} - ${currentValue.organizationName} - ${currentValue.applicationName}`;

      if(!helperGroupResultReport[key]) {
        let sumTotalApiCalls = 0
        if(this.isTwoxxChecked) sumTotalApiCalls += currentValue.twoxx
        if(this.isFourxxChecked) sumTotalApiCalls += currentValue.fourxx
        if(this.isFivexxChecked) sumTotalApiCalls += currentValue.fivexx

        const transformReport: ApiConsumptionsReportType = {
          period: currentValue.period,
          organizationId: currentValue.organizationId,
          organizationName: currentValue.organizationName,
          applicationId: currentValue.applicationId,
          applicationName: currentValue.applicationName,
          totalApiCalls: sumTotalApiCalls
        }
        helperGroupResultReport[key] = Object.assign({}, transformReport); 
        accumulator.push(helperGroupResultReport[key]);
      } 
      else
      {
        const helperTotalApiCalls = Number(helperGroupResultReport[key].totalApiCalls)
        let sumTotalApiCalls = 0
        if(this.isTwoxxChecked) sumTotalApiCalls += currentValue.twoxx
        if(this.isFourxxChecked) sumTotalApiCalls += currentValue.fourxx
        if(this.isFivexxChecked) sumTotalApiCalls += currentValue.fivexx
        const total = helperTotalApiCalls + sumTotalApiCalls
        helperGroupResultReport[key].totalApiCalls = total
      }

    
      return accumulator;
    }, []);

    this.apiConsumptionsTable = this.groupedReportsFixed
    this.filterReport()
    this.genPaginationLabel()
  }

  consumptionDetail(applicationId: string): void {
    this.loading.showLoading();

    let startDate = ""
    let endDate = ""
    let dateType = ""

    if(!this.selectedMonth && !this.selectedDay)
    {
      dateType = "year"
      startDate = dayjs(`${this.selectedYear}-05-01`).startOf("year").format("YYYY-MM-DD")
      endDate = dayjs(`${this.selectedYear}-05-01`).endOf("year").format("YYYY-MM-DD")
    }
    else if(!(this.selectedDay))
    {
      dateType = "month"
      startDate = dayjs(`${this.selectedYear}-${this.selectedMonth}-01`).startOf("month").format("YYYY-MM-DD")
      endDate = dayjs(`${this.selectedYear}-${this.selectedMonth}-01`).endOf("month").format("YYYY-MM-DD")
    }
    else
    {
      dateType = "day"
      startDate = dayjs(`${this.selectedYear}-${this.selectedMonth}-${this.selectedDay}`).format("YYYY-MM-DD")
      endDate = dayjs(`${this.selectedYear}-${this.selectedMonth}-${this.selectedDay}`).format("YYYY-MM-DD")
    }

    const requestPayload = {
      startDate: startDate,
      endDate: endDate,
      dateType: dateType,
      path: this.selectedPath ? this.selectedPath : undefined,
      applicationId: applicationId ? applicationId : undefined,
      pageFlag: "detail"
    }


    this.apiService.postConsumptionsReport(requestPayload).subscribe((response:ServiceResponse) => {
      const reportDetail = response.data;
      const groupedDetail = this.transformReportDetail(reportDetail);
      sessionStorage.setItem('reportDetail', JSON.stringify(groupedDetail));
      this.loading.hideLoading();
      this.router.navigate(['apiconsumption/report/detail']);      
    }, (err) => {
      this.handleError(err);
    }, () => {
      this.loading.hideLoading();
    })
  }

  transformReportDetail(reportDetail: any[]): any[] {
    const groupedDetail = Object.values(
      reportDetail.reduce((acc, item) => {
        const key = `${item.period}-${item.organizationName}-${item.applicationName}-${item.method}-${item.path}`;

        if (!acc[key]) {
          acc[key] = {
            period: item.period,
            organizationName: item.organizationName,
            applicationName: item.applicationName,
            apiProduct: item.apiProduct,
            method: item.method,
            path: item.method + " " + item.path,
            twoxx: 0,
            fourxx: 0,
            fivexx: 0,
            sumOfApiCall: 0
          };
        }

        acc[key].twoxx += item.twoxx;
        acc[key].fourxx += item.fourxx;
        acc[key].fivexx += item.fivexx;
        acc[key].sumOfApiCall += item.twoxx + item.fourxx + item.fivexx;

        return acc;
      }, {})
    );

    groupedDetail.sort((a, b) => {
      if (a['apiProduct'] === null) return 1;
      if (b['apiProduct'] === null) return -1;
      return a['apiProduct'].localeCompare(b['apiProduct']);
    });

    return groupedDetail;
  }

  handleError(err: any) {
    if (err.status === 404 && err.error && err.error.status.code === 1104) {
      this.loading.hideLoading();
      this.apiConsumptionsTableSearch = [];
      this.groupResultsHttpCode();
    } else {
      this.loading.showError(this.labels.current.ERROR, err.error ? err.error.status.description : err.message);
    }
  }

}
