import { Component, OnInit, Input } from "@angular/core";
import { LoadingService } from "../../shared/services/loading-service/loading.service";
import { UITextForMigratingService } from "../../services/uitext/uitext.service";
import tableHeadersArray from "./apiconsumptions-detail-table-headers.json";
import { ApiConsumptionsMasterDataService } from "../../services/apiconsumptions-masterdata/apiconsumptions-masterdata.service";
import { ActivatedRoute, Router } from '@angular/router';

@Component({
    selector: "app-comsumptions-detail",
    templateUrl: "./api-consumptions-detail.component.html",
    styleUrls: ["./api-consumptions-detail.component.less"],
    standalone: false
})
export class ApiConsumptionComponentDetail implements OnInit {
  // Table Related
  tableHeaders = tableHeadersArray;
  totalRecord = 0;
  sortBy = "apiProduct";
  isAscOrdered = true;
  paginationLabel = "";
  reportDetail: any;
  totalTwoxx: number = 0;
  totalFourxx: number = 0;
  totalFivexx: number = 0;
  totalSumOfApiCall: number = 0;
  csvExportData: any;

  constructor(
    public labels: UITextForMigratingService,
    private loading: LoadingService,
    private apiService: ApiConsumptionsMasterDataService,
    private router: Router, 
    private route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.loading.showLoading();
    const reportDetailString = sessionStorage.getItem('reportDetail');
    if (reportDetailString) {
      this.reportDetail = JSON.parse(reportDetailString);
      this.calculateTotals();
    } else {
      this.router.navigate(['apiconsumption/report']);
      this.loading.hideLoading();
    }
  }

  private calculateTotals() {
    this.totalTwoxx = this.reportDetail.reduce((sum, item) => sum + item.twoxx, 0);
    this.totalFourxx = this.reportDetail.reduce((sum, item) => sum + item.fourxx, 0);
    this.totalFivexx = this.reportDetail.reduce((sum, item) => sum + item.fivexx, 0);
    this.totalSumOfApiCall = this.reportDetail.reduce((sum, item) => sum + item.sumOfApiCall, 0);
    this.loading.hideLoading();
  }

  private convertToCSV(data: any[]): string {
    const headers = Object.keys(data[0]).filter(key => key !== 'methodPath'); // Adjust headers as needed
    const csvRows = data.map(item => 
      headers.map(header => `"${item[header]}"`).join(',')
    );
    
    return [headers.join(','), ...csvRows].join('\n');
  }

  generateHeaderSortedId(field) {
    return field
      ? "task-list-header-" +
          field.toLowerCase().split(" ").join("-").replace(".", "")
      : "";
  }

  apiConsumptionTableSortingControl() {
    if (this.isAscOrdered) {
      this.reportDetail = this.reportDetail.sort((a, b) => {
        const valA = a[this.sortBy] || '';
        const valB = b[this.sortBy] || '';
        return valA.localeCompare(valB);
      });
    } else {
      this.reportDetail = this.reportDetail.sort((a, b) => {
        const valA = a[this.sortBy] || '';
        const valB = b[this.sortBy] || '';
        return valB.localeCompare(valA);
      });
    }
  }

  sort(fieldName) {
    if (this.sortBy === fieldName) {
      this.isAscOrdered = !this.isAscOrdered;
    } else {
      this.isAscOrdered = true;
    }
    this.sortBy = fieldName;
    this.apiConsumptionTableSortingControl()
  }
  exportToCSV(): void {
    this.loading.showLoading();
    this.csvExportData = JSON.parse(JSON.stringify(this.reportDetail));
    const handleNull = (value: any) => value !== null && value !== undefined ? value : "";
    let totalApiCall = 0;
    let total2xx = 0;
    let total4xx = 0;
    let total5xx = 0;
    this.csvExportData.forEach(row => {
      delete row.period;
      delete row.applicationName;
      delete row.organizationName;
      row["API Product"] = handleNull(row.apiProduct);
      delete row.apiProduct;
      row["Path"] = handleNull(row.path);
      delete row.method;
      delete row.path;
      row["API Calls"] = handleNull(row.sumOfApiCall);
      totalApiCall += row.sumOfApiCall;
      delete row.sumOfApiCall;
      row["2xx"] = handleNull(row.twoxx);
      total2xx += row.twoxx;
      delete row.twoxx;
      row["4xx"] = handleNull(row.fourxx);
      total4xx += row.fourxx;
      delete row.fourxx;
      row["5xx"] = handleNull(row.fivexx);
      total5xx += row.fivexx
      delete row.fivexx;
    });
    this.csvExportData.push({
      "API Product": "Total API Calls",
      "Path": "",
      "API Calls": totalApiCall,
      "2xx": total2xx,
      "4xx": total4xx,
      "5xx": total5xx
    })
    const csvData = this.convertToCSV(this.csvExportData);
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
  
    const a = document.createElement('a');
    a.href = url;
    a.download = `${this.reportDetail[0].period}_${this.reportDetail[0].organizationName}_${this.reportDetail[0].applicationName}`;
    a.click();
    URL.revokeObjectURL(url);
    this.loading.hideLoading();
  }
}
