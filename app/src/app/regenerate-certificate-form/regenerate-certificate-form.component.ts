import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators, ValidatorFn } from '@angular/forms';
import { CertificateRequestComponent } from '../onboarding-eform/certificate-request/certificate-request.component';
import { LoadingService } from '../shared/services/loading-service/loading.service';
import { UITextForMigratingService } from '../services/uitext/uitext.service';
import { ValidatorService } from '../services/validator/validator.service';
import { AppService } from '../services/app/app.service';
import { CertificateService } from '../services/certificate/certificate.service';
import { Router } from '@angular/router';
import { CommonModule, DatePipe } from '@angular/common'

@Component({
    selector: 'app-regenerate-certificate-form',
    templateUrl: './regenerate-certificate-form.component.html',
    styleUrls: ['./regenerate-certificate-form.component.less'],
    standalone: false
})
export class RegenerateCertificateFormComponent implements OnInit {
  @ViewChild(CertificateRequestComponent) certificateValues: CertificateRequestComponent;
  isRegenValid: boolean = false;
  isSearchValid: boolean = false;
  appInfoValid: boolean = false;
  reasonValid: boolean = false;
  isCertError: boolean = false;
  
  form: UntypedFormGroup;
  applicationInfo;
  constructor(
    private certificateService: CertificateService,
    private applicationService: AppService,
    private fb: UntypedFormBuilder,
    private validator: ValidatorService,
    public labels: UITextForMigratingService,
    private LoadingService: LoadingService,
    private router: Router,
    private datePipe: DatePipe
    ) {
    this.form = this.fb.group({
      apiKey: ['', [
        Validators.required,
        this.validator.onlyAlphanumericalEng,
        this.validator.isApiKeyValid,
        Validators.maxLength(34)
      ]],
      reason: ['', [
        Validators.required,
        this.validator.onlyAlphaNumericalEngTh,
        Validators.maxLength(255)
      ]]
    });
   }

  ngOnInit() {
    this.applicationInfo = {};
    this.isSearchValid = false;
  }

  validateSubmit() {
    this.isRegenValid = this.certificateValues ? this.certificateValues.isCertificateValid.getValue() : false
    this.appInfoValid = this.applicationInfo && this.applicationInfo.applicationName && this.applicationInfo.organizationName;
    return this.isRegenValid && this.appInfoValid && this.form.status === 'VALID';
  }

  onChangeSearch() {
    this.isSearchValid = this.form.controls.apiKey.errors && Object.keys(this.form.controls.apiKey.errors).length > 0 ? false : true;
  }

  // get application information 
  // get certificate information by uuid return from previous request (getApplicationInfo)
  getRegenerateCertificateData(apiKey) {
    this.LoadingService.showLoading();
    this.applicationService.getApplicationInfo(apiKey)
      .subscribe((appResponse: any) => {
        if (!appResponse.success) {
          return this.LoadingService.showError(this.labels.current.ERROR, appResponse.title) ||
            this.LoadingService.showError(this.labels.current.title, appResponse.message)
        }
        if (appResponse && appResponse.data.certificateUuid.split(',').length > 1) {
          return this.LoadingService.showError(this.labels.current.REGENERATE_ERROR_APPLICATION_IS_REQUESTED, this.labels.current.REGENERATE_ERROR_APPLICATION_IS_REQUESTED_DETAILS);
        }
        if (appResponse.data.certificateUuid) {
          this.applicationInfo = appResponse.data || {};
          this.certificateService.getCertificateInfo(appResponse.data.certificateUuid)
            .subscribe(certResponse => {
              if (certResponse && !certResponse.success) {
                return this.LoadingService.showError(certResponse.title, certResponse.message) ||
                  this.LoadingService.showError(this.labels.current.ERROR, certResponse.title)
              }
              if (certResponse && certResponse.data !== null) {
                const { certificateType, certificateExpired } = certResponse.data;
                const expireDate = this.datePipe.transform(certificateExpired, 'dd/MM/yyyy');
                this.applicationInfo = {
                  ...this.applicationInfo,
                  certificateType,
                  expireDate
                }
              }
              this.LoadingService.hideLoading();
            });
        }
        else {
          this.applicationInfo = appResponse.data || {};
          this.LoadingService.hideLoading();
        }
      });
  }

  onCancel() {
    this.router.navigate(['request-management/certificate']);
  }

  createCertificate() {
    this.LoadingService.showLoading();
    const payload = {
      apiKey: this.form.controls.apiKey.value,
      certificateType: this.certificateValues.certificateType,
      certificateUuid: this.certificateValues.certificateUuid,
      csrFile: this.certificateValues.certificateType === 'CSR' ? this.certificateValues.csrFile : undefined,
      reason: this.form.controls.reason.value
    };
    this.certificateService.regenerateCertificates(payload).subscribe(res => {
      this.LoadingService.hideLoading();
      if (res.success) {
        this.LoadingService.showSuccess(this.labels.current.SUCCESS, this.labels.current.REGENERATE_SUCCESS).then(() => {
          this.isCertError = false;
          this.router.navigate(['request-management/certificate']);
        });
        return;
      } else {
        this.isCertError = true;
        const isInvalidCsr = res.message === this.labels.current.INVALID_CSR_FILE;
        this.LoadingService.showError(this.labels.current.ERROR, res.message).then(() => {
          if (isInvalidCsr) {
            this.isCertError = true;
          }
        });
        return;
      }
    }, error => { });
  }
}
