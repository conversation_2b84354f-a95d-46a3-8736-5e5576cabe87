import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { ActivatedRoute, Router, NavigationEnd } from "@angular/router";
import { UITextService } from "./shared/services/uitext-service/uitext.service";
import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";
import { LoadingService } from "./shared/services/loading-service/loading.service";
import { EMPTY, Subscription } from "rxjs";
import { AuthService } from "./shared/services/auth-service/auth.service";
import { UserService } from "./shared/services/user-service/user.service";

import { ConfigService } from "./shared/services/config-service/config.service";
import { tap } from "rxjs/operators";

import { Inject } from "@angular/core";
import { IdleService } from "./services/idle/idle.service";

@Component({
    selector: "app-root",
    templateUrl: "./app.component.html",
    styleUrls: ["./app.component.less"],
    standalone: false
})
export class AppComponent implements OnInit {
  breadcrumbs: BreadCrumb[];
  collapsed = false;
  isInit: boolean;
  env: string;
  checkExistUser: boolean;
  activeUser: any;
  pageId: string;

  private idleSub!: Subscription;

  constructor(
    private activatedRoute: ActivatedRoute,
    private http: HttpClient,
    private router: Router,
    private loading: LoadingService,
    public labels: UITextService,
    private authService: AuthService,
    private configService: ConfigService,
    private userService: UserService,
    private idleService: IdleService,
  ) {
    this.isInit = false;
    this.router.events.forEach((event) => {
      if (event instanceof NavigationEnd) {
        this.breadcrumbs = this.buildBreadCrumb(this.activatedRoute.root);
      }
    });
    this.checkExistUser = localStorage.getItem("CHECK_EXISTED_USER") === "true";
  }

  ngOnInit() {
    this.validatePrevTokens();
    this.getEnv();

    this.idleSub = this.idleService.onIdle.subscribe(() => {
      this.timeoutErrorHandler();
      const pe_me_token = localStorage.getItem("PE_ME_TOKEN");
      if (pe_me_token) {
        this.userService.logout(JSON.parse(pe_me_token).accessToken);
      } else {
        this.userService.logout(null);
      }
    });
  }

  validatePrevTokens() {
    this.authService.validateTokens().subscribe(
      (isValidToken: any) => {
        if (isValidToken) {
          this.isInit = true;
        } else {
          this.timeoutErrorHandler();
        }
      },
      () => this.timeoutErrorHandler()
    );
  }

  timeoutErrorHandler() {
    this.loading.showError(
      this.labels.current.SESSION_TIMEOUT,
      this.labels.current.SESSION_TIMEOUT_ERROR
    );
    return EMPTY;
  }

  getEnv() {
    return this.configService
      .getEnv()
      .pipe(
        tap((result) => {
          this.env = "(" + result.data + ")";
        })
      )
      .subscribe();
  }

  getTitle() {
    const breadcrumbs = this.breadcrumbs;

    const url: string = breadcrumbs[breadcrumbs.length - 1]
      ? breadcrumbs[breadcrumbs.length - 1].url.toString()
      : "";
    if (breadcrumbs && breadcrumbs.length) {
      if (url.includes("regencer")) {
        return breadcrumbs[breadcrumbs.length - 1].label.concat(
          this.env.toUpperCase()
        );
      } else return breadcrumbs[breadcrumbs.length - 1].label;
    }
    return "";
  }

  buildBreadCrumb(
    activatedRoute: ActivatedRoute,
    url: string = "",
    breadcrumbs: Array<BreadCrumb> = []
  ): Array<BreadCrumb> {
    const label =
      activatedRoute.routeConfig && activatedRoute.routeConfig.data
        ? activatedRoute.routeConfig.data.breadcrumb
        : "";
    const linkable =
      activatedRoute.routeConfig && activatedRoute.routeConfig.data
        ? activatedRoute.routeConfig.data.breadcrumb
        : false;
    const path = activatedRoute.routeConfig
      ? activatedRoute.routeConfig.path
      : "";
    const nextUrl = `${url}${path}/`;
    const breadcrumb = {
      label,
      url: nextUrl,
      linkable,
    };
    const newBreadcrumbs = breadcrumb.label
      ? [...breadcrumbs, breadcrumb]
      : breadcrumbs;
    if (activatedRoute.firstChild) {
      return this.buildBreadCrumb(
        activatedRoute.firstChild,
        nextUrl,
        newBreadcrumbs
      );
    }
    return newBreadcrumbs;
  }

  onCollapsedSidebar(bool) {
    this.collapsed = bool;
  }
}

interface BreadCrumb {
  label: string;
  url: string;
}
