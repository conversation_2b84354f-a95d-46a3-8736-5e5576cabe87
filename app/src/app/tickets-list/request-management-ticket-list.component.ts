import { Component, OnInit, Input } from "@angular/core";
import { AnalyticsService } from "../services/analytics/analytics.service";
import { LoadingService } from '../shared/services/loading-service/loading.service';
import { UITextForMigratingService } from "../services/uitext/uitext.service";
import { TicketListService } from "../services/ticket-list/ticket-list.service";
import { UserForMigratingService } from '../services/user/user.service';
import { PiiService } from "../services/pii/pii.service";

@Component({
    selector: "app-request-management-ticket-list",
    templateUrl: "./request-management-ticket-list.component.html",
    styleUrls: ["./request-management-ticket-list.component.less"],
    standalone: false
})
export class RequestManagementTicketListComponent implements OnInit {
  ticketList: any = [];
  totalItems: number = 0;
  currentPage = 1;
  pageSize: number = 10;
  expandedTickets: string[] = [];
  statuses: any = {collectionType: 'keyValue'};
  selectedStatus: string = "all";
  constructor(
    public labels: UITextForMigratingService,
    private loading: LoadingService,
    private analyticsService: AnalyticsService,
    private ticketService: TicketListService,
    private userForMigratingService:UserForMigratingService,
    private piiService:PiiService
  ) {}

  ngOnInit() {
    this.getAppList(this.currentPage, this.selectedStatus);
    this.ticketService.getStatusList().subscribe(response => {
      if (response.success && response.data) {
        const map: { [key: string]: string } = {};
        response.data.status.forEach(status => {
          if (status.displayName === 'Rejected') {
            if (!Object.values(map).includes('Rejected')) {
              map['rejected'] = 'Rejected';
            }
          } else {
            map[status.name] = status.displayName;
          }
        });
        map['all'] = 'All';
        const sortedMap = Object.keys(map)
          .sort((a, b) => map[a].localeCompare(map[b]))
          .reduce((acc, key) => {
            acc[key] = map[key];
            return acc;
          }, {} as { [key: string]: string });
    
        this.statuses = {
          collectionType: 'keyValue',
          collectionData: sortedMap,
        };
      } else {
        this.loading.showError(response.title, response.message);
      }
    });
  }

  ngAfterViewChecked() {
    //Add data-cy attribute to <app-generic-dropdown>
    for (let i = 0;i < document.getElementsByTagName('app-generic-dropdown').length;i++) {
      let dropdownCollection = document.getElementsByTagName('app-generic-dropdown',);
      let buttonCollection =dropdownCollection[i].getElementsByTagName('button');
      let label = dropdownCollection[i].getElementsByTagName('label')[0];
      for (let j = 0; j < buttonCollection.length; j++) {
        if (buttonCollection[j].className == 'dropdown-item') {
          buttonCollection[j].setAttribute('data-cy','option-' +buttonCollection[j].innerText.replace(/(\r\n|\n|\r|\s)/gm, ''));
        }
        if (buttonCollection[j].className =='btn btn-outline-primary custom-generic-dropdown dropdown-toggle') {
          buttonCollection[j].setAttribute('data-cy','dropdown-' + label.innerText.replace(/(\r\n|\n|\r|\s)/gm, ''));
        }
      }
    }
  }

  onPageChange(page) {
    this.currentPage = page;
    this.getAppList(this.currentPage, this.selectedStatus);
  }

  getAppList(page: number,status: string) {
    this.loading.showLoading();
    this.ticketService.getTicketList(page, status).subscribe((response: any) => {
      if (response.success) {
        for(let data of response.data.tickets){
          const postData = {
            parameter: `${data.requesterName} ${data.requesterLastName} ${data.requesterEmail}`,
            newValue: '',
            recordKeyValue: '',
            previousValue: ''
          };
          this.piiService.postSensitive(postData,'View Ticket Info');
        }
        this.loading.hideLoading();
        this.ticketList = response.data.tickets;
        this.totalItems = response.data.totalRecord;
      } else {
        const msg = response.message || this.labels.current.UNEXPECTED_SERVER_ERROR;
        this.loading.showError(this.labels.current.ERROR, msg);
      }
    });
  }

  showDetail(ticketId: string) {
    if (this.expandedTickets.includes(ticketId)) {
      this.expandedTickets.splice(this.expandedTickets.indexOf(ticketId), 1);
    } else {
      this.expandedTickets.push(ticketId);
    }
  }

  checksubtaskStatus(data){
    if(data.subtasks[0] && data.subtasks[0].subtaskStatus=='Submitted'){
      return true
    }
    return false
  }
}
