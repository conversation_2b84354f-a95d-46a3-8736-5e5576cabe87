import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { UITextForMigratingService } from "../services/uitext/uitext.service";

@Component({
    selector: 'app-industry-dropdown',
    templateUrl: './industry-dropdown.component.html',
    styleUrls: ['./industry-dropdown.component.less'],
    standalone: false
})
export class IndustryDropdownComponent implements OnInit {

  @Input() industries: any;
  @Input() customLabel: string;
  @Input() hasError: boolean = false;
  @Input() hasRequiredError: boolean = false;
  @Input() readonly: boolean = false;
  @Input() selectedIndustry = '';

  @Output() touch = new EventEmitter<void>();
  @Output() itemClick = new EventEmitter<any>();

  constructor(public labels: UITextForMigratingService) { }

  ngOnInit() {
  }

  getSelectedIndustry(): string {
    if (!this.industries) {
      return '';
    }
    return (this.industries.find(industry => industry.industrialCode === parseInt(this.selectedIndustry, 10)) || {industrialName: ''}).industrialName;
  }

  openChanged(isOpen: boolean) {
    if (!isOpen) {
      this.touch.emit();
    }
  }

  itemClicked(industrialCode) {
    this.itemClick.emit({
      industrialCode: industrialCode
    });
  }

}
