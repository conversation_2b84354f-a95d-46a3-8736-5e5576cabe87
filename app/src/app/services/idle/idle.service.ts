import { Injectable, NgZone } from "@angular/core";
import { Subject, fromEvent, merge, timer } from "rxjs";
import { startWith, switchMap, throttleTime } from "rxjs/operators";

const IDLE_TIME = 900000;
const IDLE_TRICK_ACTIVE_TIME = 1000;
const IDLE_ACTIVE_STORAGE = "APP_IDLE_ACTIVE_TIME";

@Injectable({
  providedIn: "root",
})
export class IdleService {
  private activityEvents = [
    "mousemove",
    "mousedown",
    "keydown",
    "scroll",
    "touchstart",
  ];
  private IDLE = "idle";
  private reset$ = new Subject<void>();
  public onIdle = new Subject<string>();

  constructor(private ngZone: NgZone) {
    this.ngZone.runOutsideAngular(() => {
      const activity$ = merge(
        ...this.activityEvents.map((e) => fromEvent(window, e))
      );
      activity$.pipe(throttleTime(IDLE_TRICK_ACTIVE_TIME)).subscribe(() => {
        localStorage.setItem(IDLE_ACTIVE_STORAGE, new Date().toISOString());
        this.resetIdleTimer();
      });

      window.addEventListener("storage", (event: StorageEvent) => {
        if (event.key === IDLE_ACTIVE_STORAGE && event.newValue) {
          this.resetIdleTimer();
        }
      });

      this.startIdleTimer();
    });
  }

  private resetIdleTimer = () => {
    this.reset$.next()
  }

  private startIdleTimer = () => {
    this.reset$.pipe(
      startWith(0), // Start the timer immediately
      switchMap(() => timer(IDLE_TIME)),
    ).subscribe(() =>
      this.onIdle.next(this.IDLE)
    );
  };
}
