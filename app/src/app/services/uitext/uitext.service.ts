import { Injectable } from '@angular/core';
import texts from '../../text/text.json';

@Injectable()
export class UITextForMigratingService {
  private currentLanguage = 'en';

  constructor() { }

  get current (): any {
    return texts["en"];
  }

  getLanguage (): string {
    return this.currentLanguage;
  }

  getText(language): any {
    return texts[language];
  }
  setLanguage (lang: string) {
    if (lang === 'en' || lang === 'th') {
      this.currentLanguage = lang;
    }
  }

  fill (label: string, ...values: any[]): string {
    for (let i = 0; i < values.length; i++) {
      label = label.replace('{' + i + '}', values[i] || '');
    }
    return label;
  }

  breakLines (input: string): string {
    return input.split('\n').join('<br>');
  }
}
