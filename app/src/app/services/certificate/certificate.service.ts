import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { ConfigService } from '../config/config.service';
import { Observable, of } from 'rxjs';
import { ErrorService } from '../error/error.service';
import { ActionResponse } from '../../models/action-response';
import { ServiceResponse } from '../../models/service-response';
import { UITextForMigratingService } from '../uitext/uitext.service';
import { map, catchError } from "rxjs/operators";
import { GetAADGroupResponse } from './certificate.interface';

@Injectable(
  {providedIn: "root"}
)
export class CertificateService {

  constructor(private httpClient: HttpClient,
    private config: ConfigService,
    private errorService: ErrorService,
    private labels: UITextForMigratingService
  ) { }

  private getUrlConfig = (pathKey: string = '') => (this.config.current.urls[pathKey] || {});

  private getUrl = (urlConfig: any) => (
    `${this.config.current.apiDomain}${this.config.current.urls.baseUrl}${urlConfig.path || ''}`
  )

  getHttpParams = (params: any = {}) => {
    let httpParams = new HttpParams();
    const keys = Object.keys(params);
    for (const key of keys) {
      httpParams = httpParams.set(key, params[key]);
    }
    return httpParams;
  }

  getCertificateInfo(uuid: string): Observable<ActionResponse> {
    const urlConfig = this.getUrlConfig('getCertificateRegenerateByUuid');
    const url = `${this.getUrl(urlConfig)}/${uuid}/regenerate`;
    const params = this.getHttpParams({ type: 'certificateUuid', status: 'closed' });
    return this.httpClient[urlConfig.verb](url, { params }).pipe(
      map((response: ServiceResponse) => {
        const status = response.status;
        if (status.code === 1000) {
          return {
            success: true,
            data: response.data
          };
        } else {
          const message = status.code === 1999 ? status.descripton : this.labels.fill(this.labels.current.UNKNOWN_SERVER_RESPONSE, `${status.code}`) || '';
          return {
            success: false,
            message: message
          };
        } 
      }), catchError((response: HttpErrorResponse) => {
        if(response.error && response.error.status){
          const status = response.error.status;
          if(status.code === 1104){
            return of({
              success: true,
              data: null
            });
          }
        }
        const result = this.errorService.getErrorActionResponse(response, urlConfig);
        return of(result);
      })
    )
  }

  getCertificateList(offSet, limit, sortBy, sortDesc, requestNo, aadGroup): Observable<any> {
    const urlConfig = this.getUrlConfig('getCertificateRegenerate');
    const url = `${this.getUrl(urlConfig)}`;
    let paramsData = {offSet: offSet, limit: limit, sortBy: sortBy, sortDesc: sortDesc}
    if (requestNo) {
      paramsData['certificateTicketNo'] = requestNo
    }
    if (aadGroup) {
      paramsData['businessUnit'] = aadGroup
    }
    const params = this.getHttpParams(paramsData);

    const result =  this.httpClient.get(url, { params })
    return result

    // return this.httpClient[urlConfig.verb](url, {params}).map((response: ServiceResponse) => {
    //   const status = response.status;
    //   if (status.code === 1000) {
    //     return {
    //       success: true,
    //       data: response.data
    //     };
    //   }
    //   else {
    //     const message = status.code === 1999 ? status.descripton : this.labels.fill(this.labels.current.UNKNOWN_SERVER_RESPONSE, `${status.code}`) || '';
    //     return {
    //       success: false,
    //       message: message
    //     };
    //   }
    // }).catch((response: HttpErrorResponse) => {
    //   const result = this.errorService.getErrorActionResponse(response, urlConfig);
    //   return of(result);
    // });
  }

  private convertToFormData(payload) {
    const payloadAsForm = new FormData();
    // tslint:disable-next-line: forin
    for (const key in payload) {
      payloadAsForm.append(key, payload[key]);
    }
    return payloadAsForm;
  }

  regenerateCertificates(payload): Observable<ActionResponse> {
    const urlConfig = this.getUrlConfig('postRegenerateCertificate');
    const url = `${this.getUrl(urlConfig)}`;

    const formPayload = this.convertToFormData(payload);
    return this.httpClient[urlConfig.verb](url, formPayload).pipe(
      map((response: ServiceResponse) => {
        const status = response.status;
        if (status.code === 1000) {
          return {
            success: true,
            data: response.data
          };
        }
        else {
          const message = status.code === 1999 ? status.descripton : this.labels.fill(this.labels.current.UNKNOWN_SERVER_RESPONSE, `${status.code}`) || '';
          return {
            success: false,
            message: message
          };
        }
      }), catchError((response: HttpErrorResponse) => {
        const result = this.errorService.getErrorActionResponse(response, urlConfig);
        return of(result);
      })
    )
  }

  getAADGroup(aadGroupUuid = undefined): Observable<GetAADGroupResponse> {
    const urlConfig = this.getUrlConfig('getAADGroupList');
    const url = `${this.getUrl(urlConfig)}`;
    return this.httpClient[urlConfig.verb](url).pipe(
      map((response: ServiceResponse) => {
        const status = response.status;
        if (status.code === 1000) {
          return {
            success: true,
            data: response.data
          };
        }
        else {
          const message = status.code === 1999 ? status.descripton : this.labels.fill(this.labels.current.UNKNOWN_SERVER_RESPONSE, `${status.code}`) || '';
          return {
            success: false,
            message: message
          };
        }
      }), catchError((response: HttpErrorResponse) => {
        const result = this.errorService.getErrorActionResponse(response, urlConfig);
        return of(result);
      })
    )
  }

}


