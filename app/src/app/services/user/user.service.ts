import {
  Injectable,
  EventEmitter,
  Inject,
} from '@angular/core';
import { User } from '../../models/user';
import { ServiceResponse } from '../../models/service-response';
import {
  HttpClient,
  HttpErrorResponse,
  HttpParams,
} from '@angular/common/http';
import { ConfigService } from '../config/config.service';
import { Observable, of } from 'rxjs';
import { AuthData } from '../../models/auth-data';
import * as jp from 'jsonpath';


import { UITextForMigratingService } from "../../services/uitext/uitext.service";
import { ErrorService } from '../error/error.service';
import { ActionResponse } from '../../models/action-response';
import { LoadingService } from '../../shared/services/loading-service/loading.service';
import { map, catchError } from 'rxjs/operators';
import { CookieService } from 'ngx-cookie-service';
import { jwtDecode } from "jwt-decode";

import { UserService } from "../../../../src/app/shared/services/user-service/user.service";

const KEY_USER_DATA = 'PE_USER_DATA';
const KEY_AUTH_PERMISSION = 'PE_AUTH_PERMISSION';
const PE_AUTH_DATA = 'PE_AUTH_DATA';


@Injectable()
export class UserForMigratingService {
  authData: AuthData = null;
  currentUser: User = null;
  currentConfig: any;
  cachedIndustries: any = null;
  isUserFetched: boolean = false;
  accessToken: any = null

  loginStatus: EventEmitter<boolean> = new EventEmitter<boolean>();

  userInfoUpdated: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    private http: HttpClient,
    private config: ConfigService,
    private errorService: ErrorService,
    private labels: UITextForMigratingService,
    private loading: LoadingService,
    private cookieService: CookieService,
    private userService: UserService
  ) {
    this.authData = localStorage.getItem(KEY_AUTH_PERMISSION) ? { permissions: localStorage.getItem(KEY_AUTH_PERMISSION) } : null;
    this.currentUser = JSON.parse(localStorage.getItem(KEY_USER_DATA));
    this.currentConfig = this.config.current;
    const accessAuthData = localStorage.getItem(PE_AUTH_DATA)
    if(accessAuthData)
    {
      this.accessToken = JSON.parse(localStorage.getItem(PE_AUTH_DATA)).accessToken
    }


    // Set username for Google analytics after user comeback and jwt's still not expired.
    if (this.currentUser) {
      const userUuid = this.currentUser.userUuid;
      (<any>window).userUuid = userUuid;
      // angulartics2GoogleAnalytics.setUsername(userUuid);
    }
  }

  getSessionInfoBy = () => {

    const sessionInfo:any = jwtDecode(this.accessToken)
    return {
      name: sessionInfo.name,
      email: sessionInfo.unique_name,
      azureUuid: sessionInfo.oid
    }
  }

  getHttpParams = (params: any = {}) => {
    let httpParams = new HttpParams();
    const keys = Object.keys(params);
    for (const key of keys) {
      httpParams = httpParams.set(key, params[key]);
    }
    return httpParams;
  };

  getUrlConfig = (pathKey: string = '') =>
    this.currentConfig.urls[pathKey] || {};

  getUrl = (urlConfig: any) =>
    `${this.currentConfig.apiDomain}${this.currentConfig.urls.baseUrl}${
      urlConfig.path || ''
    }`;

  requestLogin() {
    this.loading.showError(
      this.labels.current.SESSION_TIMEOUT,
      this.labels.current.SESSION_TIMEOUT_ERROR,
    );
  }

  login(username: string, password: string): Observable<ActionResponse> {
    const urlConfig = this.getUrlConfig('login');
    const payload = {
      userName: username,
      password: password,
    };
    const postReq = this.http.post<ServiceResponse>(
      this.getUrl(urlConfig),
      payload,
      {
        responseType: 'json',
      },
    );
    return postReq.pipe
    (
      map(response => {
        if (response.status.code === 1000) {
          this.authData = {
            permissions: response.data.permissions,
          };

          // Set username for Google analytics after login.
          // this.angulartics2GoogleAnalytics.setUsername(
          //   response.data.userDetails.userUuid,
          // );
          (<any>window).userUuid = response.data.userDetails.userUuid;

          // Extract user info from the login response
          this.currentUser = {
            firstName: response.data.userDetails.firstName,
            lastName: response.data.userDetails.lastName,
            organizationName: '',
            email: response.data.userDetails.email,
            industrialCode: '',
            userUuid: response.data.userDetails.userUuid,
          };

          // set jwt in cookie by response header
          localStorage.setItem(KEY_USER_DATA, JSON.stringify(this.currentUser));
          localStorage.setItem(KEY_AUTH_PERMISSION, JSON.stringify(this.authData.permissions));

          return { success: true };
        } else if (response.status.code === 1999) {
          return {
            success: false,
            message: this.labels.current.INVALID_USERNAME_PASSWORD,
          };
        }
        return {
          success: false,
          message: this.labels.fill(
            this.labels.current.UNKNOWN_SERVER_RESPONSE,
            `${response.status.code}`,
          ),
        };
      })
      , catchError((response: HttpErrorResponse) =>
        of(this.errorService.getErrorActionResponse(response, urlConfig)),
      )
    )
      
  }

  getAuthData() {
    return this.authData
  }

  getCurrentUser() {
    return this.currentUser;
  }

  getUserUuid() {
    if (this.currentUser) {
      return this.currentUser.userUuid;
    }
    else {
      const userInfo = this.getSessionInfoBy()
      if(userInfo.azureUuid) return userInfo.azureUuid
      else return 'Anonymous';
    }
  }

  hasPermission(path) {
    const permissions = localStorage.getItem(KEY_AUTH_PERMISSION);
    if (permissions && Object.keys(permissions).includes(path)) {
      // Currently only using one permission value --> 'all'
      return permissions[path].includes('all');
    }
    return false;
  }

  logout(): Observable<void> {
    // Do not care if there is error when logging out
    const urlConfig = this.getUrlConfig('logout');
    return this.http
      .post<ServiceResponse>(this.getUrl(urlConfig), {})
      .pipe
      (
        map(() => this.clearUserData())
        , catchError(() => of(this.clearUserData()))
      )

  }

  clearUserData() {
    // delete auth data in cookie by response header
    this.currentUser = null;
    this.isUserFetched = false;
    localStorage.removeItem(KEY_USER_DATA);
    localStorage.removeItem(KEY_AUTH_PERMISSION);
    this.loginStatus.emit(true);
  }

  preSignUp(
    firstName: string,
    lastName: string,
    industrialCode: string,
    email: string,
    mobileNumber: string,
    captchaResponse: string,
  ): Observable<ActionResponse> {
    const urlConfig = this.getUrlConfig('preSignUp');
    const user = {
      firstName,
      lastName,
      email,
      industrialCode,
      mobile: mobileNumber,
      captchaResponse,
    };
    return this.http
      .post<ServiceResponse>(this.getUrl(urlConfig), user)
      .pipe(
        map(response => {
          if (response.status && response.status.code === 1000) {
            return { success: true };
          } else if (response.status && response.status.code === 1999) {
            return { success: false };
          }
          return {
            success: false,
            message: this.labels.fill(
              this.labels.current.UNKNOWN_SERVER_RESPONSE,
              `${response.status.code}`,
            ),
          };
        })
        , catchError((response: HttpErrorResponse) => {
          if (response.status === 400) {
            const errorNode = jp.query(response, '$..error.type');
            if (errorNode && Array.isArray(errorNode) && errorNode.length > 0) {
              const errorType = <string>errorNode[0];
              if (errorType === 'duplicated_entry') {
                return of({
                  success: false,
                  message: this.labels.fill(
                    this.labels.current.DUPLICATE_EMAIL,
                    email,
                  ),
                });
              }
            }
          }
          return of(
            this.errorService.getErrorActionResponse(response, urlConfig),
          );
        })
      )
      
  }

  signUp(data: any, token: string): Observable<ActionResponse> {
    const urlConfig = this.getUrlConfig('signUp');
    return this.http
      .post<ServiceResponse>(this.getUrl(urlConfig), data, {
        params: this.getHttpParams({ token }),
      })
      .pipe
      (
        map(response => {
          if (response.status && response.status.code === 1000) {
            return { success: true };
          } else if (response.status && response.status.code === 1999) {
            return { success: false };
          }
          return {
            success: false,
            message: this.labels.fill(
              this.labels.current.UNKNOWN_SERVER_RESPONSE,
              `${response.status.code}`,
            ),
          };
        })
        , catchError((response: HttpErrorResponse) => {
          if (response.status === 400) {
            const errorNode = jp.query(response, '$..error.type');
            if (errorNode && Array.isArray(errorNode) && errorNode.length > 0) {
              const errorType = <string>errorNode[0];
              let errMessage = '';
              if (errorType === 'weak_password') {
                errMessage = this.labels.current.WEAK_PASSWORD;
              } else if (errorType === 'conflict_org_name') {
                errMessage = this.labels.fill(
                  this.labels.current.DUPLICATE_ORG_NAME,
                  data.organizationName,
                );
              } else {
                errMessage = this.labels.current.UNEXPECTED_SERVER_ERROR;
              }
              return of({
                success: false,
                message: errMessage,
              });
            } else {
              return of({
                success: false,
                message: this.labels.current.UNEXPECTED_SERVER_ERROR,
              });
            }
          } else {
            return of(
              this.errorService.getErrorActionResponse(response, urlConfig),
            );
          }
        })
      )
      
  }

  checkEmailNotTaken(email: string) {
    const urlConfig = this.getUrlConfig('validateEmailNotTaken');
    return this.http
      .post<ServiceResponse>(this.getUrl(urlConfig), { email })
      .pipe
      (
        map(response => {
          if (response.status && response.status.code === 1000) {
            return {
              success: true,
              data: true,
            };
          }
          return {
            success: false,
            message: this.labels.current.UNEXPECTED_SERVER_ERROR,
          };
        })
        , catchError((response: HttpErrorResponse) =>
          of(
            response.status === 400
              ? {
                  success: true,
                  data: false,
                  message: null,
                }
              : this.errorService.getErrorActionResponse(response, urlConfig),
          ),
        )
      )
      
  }

  getTermsOfUse(): Observable<ActionResponse> {
    const urlConfig = this.getUrlConfig('termsOfUse');
    return this.http
      .get<ServiceResponse>(this.getUrl(urlConfig))
      .pipe
      (
        map(response =>
          response.status && response.status.code === 1000
            ? {
                success: true,
                data: response.data,
              }
            : {
                success: false,
                message: this.labels.current.UNEXPECTED_SERVER_ERROR,
              },
        )
        , catchError((response: HttpErrorResponse) =>
          of(this.errorService.getErrorActionResponse(response, urlConfig)),
        )
      )
      
  }

  getSecretQuestions(): Observable<ActionResponse> {
    const urlConfig = this.getUrlConfig('secretQuestions');
    return this.http
      .get<ServiceResponse>(this.getUrl(urlConfig))
      .pipe
      (
        map(response =>
          response.status && response.status.code === 1000
            ? {
                success: true,
                data: response.data,
              }
            : {
                success: false,
                message: this.labels.current.TRY_AGAIN_ERROR,
              },
        )
        , catchError((response: HttpErrorResponse) =>
          of(this.errorService.getErrorActionResponse(response, urlConfig)),
        )
      )
      
  }

  getIndustriesList(): Observable<ActionResponse> {
    if (this.cachedIndustries) {
      return of({
        success: true,
        data: this.cachedIndustries,
      });
    }
    const urlConfig = this.getUrlConfig('industries');
    return this.http
      .get<ServiceResponse>(this.getUrl(urlConfig))
      .pipe
      (
        map(response => {
          if (response.status && response.status.code === 1000) {
            this.cachedIndustries = response.data;
            return {
              success: true,
              data: response.data,
            };
          } else {
            return {
              success: false,
              message: this.labels.current.TRY_AGAIN_ERROR,
            };
          }
        })
        , catchError((response: HttpErrorResponse) =>
          of(this.errorService.getErrorActionResponse(response, urlConfig)),
        )
      )
      
  }

  getUser(): Observable<ActionResponse> {
    if (this.isUserFetched) {
      return of({
        success: true,
        data: this.currentUser,
      });
    }
    const urlConfig = this.getUrlConfig('getUserInfo');
    return this.http
      .get<ServiceResponse>(this.getUrl(urlConfig))
      .pipe
      (
        map(response => {
          if (response.status && response.status.code === 1000) {
            this.isUserFetched = true;
            this.userInfoUpdated.emit();
  
            const responseData = response.data;
            this.currentUser.firstName = responseData.firstName;
            this.currentUser.lastName = responseData.lastName;
            this.currentUser.organizationName = responseData.organizationName;
            this.currentUser.email = responseData.email;
            this.currentUser.industrialCode = responseData.industrialCode;
            localStorage.setItem(KEY_USER_DATA, JSON.stringify(this.currentUser));
            return {
              success: true,
              data: this.currentUser,
            };
          } else {
            // Force re-fetch if it failed the first time.
            this.isUserFetched = false;
            return {
              success: false,
              message: this.labels.current.TRY_AGAIN_ERROR,
            };
          }
        })
        , catchError((response: HttpErrorResponse) =>
          of(this.errorService.getErrorActionResponse(response, urlConfig)),
        )
      )
      
  }

  getRegistrationData(token: string): Observable<ActionResponse> {
    const urlConfig = this.getUrlConfig('getPreSignUpData');
    return this.http
      .get<ServiceResponse>(this.getUrl(urlConfig), {
        params: this.getHttpParams({ token }),
      })
      .pipe
      (
        map(response => {
          if (response.status && response.status.code === 1000) {
            return {
              success: true,
              data: response.data,
            };
          }
          return {
            success: false,
            message: this.labels.current.UNEXPECTED_SERVER_ERROR,
          };
        })
        , catchError((response: HttpErrorResponse) =>
          of(this.errorService.getErrorActionResponse(response, urlConfig)),
        )
      )
      
  }

  updateUserInfo(
    firstName: string,
    lastName: string,
    industrialCode: string,
  ): Observable<ActionResponse> {
    const urlConfig = this.getUrlConfig('updateUserInfo');
    const payload = {
      firstName: firstName,
      lastName: lastName,
      industrialCode: industrialCode,
    };
    return this.http
      .put<ServiceResponse>(this.getUrl(urlConfig), payload)
      .pipe
      (
        map(response => {
          if (response.status && response.status.code === 1000) {
            this.syncUserResponseData(response);
            this.userInfoUpdated.emit();
            return { success: true };
          } else {
            return {
              success: false,
              message: this.labels.current.UNEXPECTED_SERVER_ERROR,
            };
          }
        })
        , catchError((response: HttpErrorResponse) =>
          of(this.errorService.getErrorActionResponse(response, urlConfig)),
        )
      )
      
  }

  updatePassword(
    oldPassword: string,
    newPassword: string,
  ): Observable<ActionResponse> {
    const currentUser = this.getCurrentUser();
    const urlConfig = this.getUrlConfig('updatePassword');
    const payload = {
      firstName: currentUser.firstName,
      lastName: currentUser.lastName,
      oldPassword: oldPassword,
      newPassword: newPassword,
    };

    return this.http
      .put<ServiceResponse>(this.getUrl(urlConfig), payload)
      .pipe
      (
        map(response => {
          if (response.status && response.status.code === 1000) {
            this.syncUserResponseData(response);
            return { success: true };
          }
          return {
            success: false,
            message: this.labels.current.UNEXPECTED_SERVER_ERROR,
          };
        })
        , catchError((response: HttpErrorResponse) =>
          of(this.errorService.getErrorActionResponse(response, urlConfig)),
        )
      )
      
  }

  verifyEmailStatus(email: string): Observable<ActionResponse> {
    const urlConfig = this.getUrlConfig('verifyEmailStatus');
    const payload = {
      email: email,
    };
    const postReq = this.http.post<ServiceResponse>(
      this.getUrl(urlConfig),
      payload,
      {
        responseType: 'json',
      },
    );
    return postReq
      .pipe
      (
        map(response => {
          if (response.status.code == 1000) {
            if (response.data.status === 'verified') {
              return { success: true };
            } else if (response.data.status === 'expired') {
              return {
                success: false,
                data: 'This email address has already expired.',
              };
            } else if(response.data.status == 'pending' || "pending can't enrolled email") {
              return {
                success: false,
                data: 'This email address is in the verification process.',
              };
            } else
              return {
                success: false,
                data: 'This email address does not exist.',
              };
          }
          return {
            success: false,
            data: 'Server error, please try again.',
          };
        })
        , catchError((response: HttpErrorResponse) => {
          let message = 'Server error, please try again.';
          if (response.status == 404)
            message = 'This email address does not exist.';
          return of({
            success: false,
            message: message,
          });
        })
      )
      
  }

  syncUserResponseData(response) {
    if (response.data) {
      if (response.data.userDetails) {
        this.currentUser.firstName = response.data.userDetails.firstName;
        this.currentUser.lastName = response.data.userDetails.lastName;
        this.currentUser.industrialCode =
          response.data.userDetails.industrialCode;
      }

      // set jwt in cookie by response header

      // Save data to storage
      localStorage.setItem(KEY_USER_DATA, JSON.stringify(this.currentUser));
    }
  }

  getUserRole() {
    const userRole = this.userService.userInfo.role
    return userRole ? userRole : ""
  }

}
