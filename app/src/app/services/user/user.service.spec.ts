import { TestBed, inject } from '@angular/core/testing';

import { UserForMigratingService } from './user.service';
import { ConfigService } from '../config/config.service';
import { HttpClientModule } from '@angular/common/http';
import { UITextForMigratingService } from '../uitext/uitext.service';
import { ErrorService } from '../error/error.service';
import { LoadingService } from '../../shared/services/loading-service/loading.service';

describe('UserForMigratingService', () => {
  let textServiceStub: Partial<UITextForMigratingService>;
  let errorServiceStub: Partial<ErrorService>;

  beforeEach(() => {
    const configSpy = jasmine.createSpyObj('ConfigService', ['current']);
    const storageSpy = jasmine.createSpyObj('StorageService', ['get']);
    const loadingSpy = jasmine.createSpyObj('LoadingService', ['showError']);

    textServiceStub = {
      current: {}
    };
    errorServiceStub = {
      getErrorActionResponse: () => null
    };

    TestBed.configureTestingModule({
      imports: [HttpClientModule],
      providers: [
        UserForMigratingService,
        { provide: ConfigService, useValue: configSpy },
        { provide: UITextForMigratingService, useValue: textServiceStub },
        { provide: ErrorService, useValue: errorServiceStub },
        { provide: LoadingService, useValue: loadingSpy }
      ]
    });
  });

  it('should be created', inject([UserForMigratingService], (service: UserForMigratingService) => {
    expect(service).toBeTruthy();
  }));
});
