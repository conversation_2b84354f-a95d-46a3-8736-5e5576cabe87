import { Component, OnInit } from '@angular/core';
import dayjs from 'dayjs';
import { TaskService } from '../services/task-service/task.service';
import { UserService } from '../../shared/services/user-service/user.service';
import { LoadingService } from '../../shared/services/loading-service/loading.service';
import { UITextService } from '../../shared/services/uitext-service/uitext.service';
import tableHeadersArray from './table-headers.json';

@Component({
    selector: 'app-task-list',
    templateUrl: './task-list.component.html',
    styleUrls: ['./task-list.component.less'],
    standalone: false
})
export class TaskListComponent implements OnInit {
  tableHeaders = tableHeadersArray;
  totalRecord = 0;
  subTasks = [];
  pageSize = 10;
  page = 1;
  sizeOptions = [10, 20, 50, 100];
  debounce: any;
  role: string;
  sortBy = 'updatedDate';
  isAscOrdered = false;
  paginationLabel = '';

  constructor(
    private taskService: TaskService,
    private userService: UserService,
    private loading: LoadingService,
    public labels: UITextService
  ) { }

  ngOnInit() {
    this.userService.getUserInfo().subscribe(userInfo => {
      if (userInfo && userInfo.approvalGroup) {
        this.role = userInfo.approvalGroup.toUpperCase();
        this.getSubtasks();
      }
    });
  }

  formatTime(timeInMilli) {
    return dayjs.unix(timeInMilli).format('DD/MM/YYYY');
  }

  range(num) {
    const result = [];
    for (let i = 1; i <= num; i++) {
      result.push(i);
    }
    return result;
  }

  calcOffsetLimit() {
    const offset = (this.page - 1) * this.pageSize;
    return {
      offset,
      limit: this.pageSize
    };
  }

  debounceGetSubTasks() {
    clearTimeout(this.debounce);
    this.debounce = setTimeout(() => {
      this.getSubtasks();
    }, 100);
  }

  onPageChange() {
    this.debounceGetSubTasks();
  }

  sort(fieldName) {
    if (this.sortBy === fieldName) {
      this.isAscOrdered = !this.isAscOrdered;
    } else {
      this.isAscOrdered = true;
    }

    this.sortBy = fieldName;
    this.debounceGetSubTasks();
  }

  selectPageSize(sizeOption) {
    this.pageSize = sizeOption;
    this.debounceGetSubTasks();
  }

  shouldDisabled() {
    return this.totalRecord < this.sizeOptions[0];
  }

  genPaginationLabel() {
    let startIndex = this.pageSize * (this.page - 1) + 1;
    const lastIndex = startIndex + this.subTasks.length - 1;

    if (this.subTasks.length === 0) { startIndex = 0; }
    this.paginationLabel = `Showing ${startIndex}-${lastIndex} of ${this.totalRecord}`;
  }

  getSubtasks() {
    const { offset, limit } = this.calcOffsetLimit();

    const params = {
      role: this.role,
      offset,
      limit,
      sortBy: this.sortBy,
      sortDesc: this.isAscOrdered ? 'ASC' : 'DESC'
    };

    this.loading.showLoading();
    this.taskService.getSubtaskByRole(params).subscribe(
      result => {
        this.loading.hideLoading();
        if (result.data) {
          const { totalRecord, subTasks } = result.data;
          this.totalRecord = totalRecord;
          this.subTasks = subTasks;
          this.genPaginationLabel();
        }
      },
      err => {
        this.loading.hideLoading();
        this.loading.showError(this.labels.current.TASK_LIST_ERROR_TITLE, this.labels.current.TASK_LIST_ERROR_DETAIL);
      }
    );
  }

  generateHeaderSortedId(field) {
    return field ? 'task-list-header-' + field.toLowerCase().split(' ').join('-').replace('.', '') : '';
  }

  generatePageSizeOptionId(sizeOption) {
    return sizeOption ? 'task-list-display-item-' + sizeOption : '';
  }
}
