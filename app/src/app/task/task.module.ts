import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';

import { TaskRoutingModule } from './task-routing.module';
import { TaskComponent } from './task.component';
import { TaskListComponent } from './task-list/task-list.component';
import { TaskDetailComponent } from './task-detail/task-detail.component';
import { TaskActionComponent } from './task-detail/components/task-action/task-action.component';
import { UITextService } from '../shared/services/uitext-service/uitext.service';
import { SharedModule } from '../shared/shared.module';

@NgModule({
  declarations: [TaskComponent, TaskListComponent, TaskDetailComponent, TaskActionComponent],
  imports: [CommonModule, TaskRoutingModule, NgbModule, SharedModule, FormsModule, ReactiveFormsModule],
  providers: [UITextService]
})
export class TaskModule {}
