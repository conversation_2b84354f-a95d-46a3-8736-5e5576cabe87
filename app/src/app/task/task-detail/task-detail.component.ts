import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

import { TaskService } from '../services/task-service/task.service';
import { LoadingService } from '../../shared/services/loading-service/loading.service';
import { UITextService } from '../../shared/services/uitext-service/uitext.service';

import dayjs from 'dayjs';
import { of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import { PiiService } from "../../services/pii/pii.service";
import { Inject } from "@angular/core";
@Component({
    selector: 'app-task-detail',
    templateUrl: './task-detail.component.html',
    styleUrls: ['./task-detail.component.less'],
    standalone: false
})
export class TaskDetailComponent implements OnInit {
  taskId;
  taskData;
  taskActionHistory;
  scopesRef;
  formattedTaskScopes;
  accordionIdList;
  rejectedSubTask = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private loading: LoadingService,
    public taskService: TaskService,
    public labels: UITextService,
    private piiService:PiiService,
  ) {}

  ngOnInit() {
    this.taskId = this.route.snapshot.paramMap.get('taskId');
    this.getAllData();
  }

  getAllData() {
    this.loading.showLoading();
    let isTaskDetailError = false;
    let isTaskActivityError = false;

    this.taskService.getSubTaskDetails(this.taskId).pipe(
      mergeMap((result) => {
        if (result && !result.success) {
          isTaskDetailError = true;
        } else {
          this.taskData = result.data;
          this.getCsrName(this.taskData.certificate_section.csrFile)
          this.rejectedSubTask = this.taskService.closedTicketStatus.includes(this.taskData.subTaskStatusName) ? true : false;
        }
        return this.taskService.getSubTaskHistory(this.taskId).pipe(
          mergeMap((result2) => {
            if (result2 && !result2.success) {
              isTaskActivityError = true;
            } else {
              this.taskActionHistory = result2.data;
            }
            const postData = {
              parameter: `${this.taskData.general_section.corporateTaxId} ${this.taskData.general_section.email} ${this.taskData.general_section.contactPerson} ${this.taskData.general_section.mobileNumber}`,
              newValue: '',
              recordKeyValue: '',
              previousValue: ''
            };
            this.piiService.postSensitive(postData,this.getEventTypeByRole());
            return of([this.taskData, this.taskActionHistory])
          })
        )
      })
    ).subscribe(([subtaskDetail, subtaskActivity]) => {
      if (isTaskDetailError) {
        this.loading.showError(this.labels.current.ERROR, this.labels.current.NOT_FOUND_ERROR).then(() => this.router.navigate(['task']));
      } else if (isTaskActivityError) {
        this.loading.showError(subtaskActivity.title, subtaskActivity.message);
      }
    });
  }

  formatDate(date) {
    return dayjs(date).format('DD/MM/YYYY');
  }

  getCsrName(csrFile){
    const csrFilePart1 = csrFile.split("/")[6]
    if (csrFilePart1) {
      const csrFilePart2 = csrFilePart1.split("_")[1]
      if (csrFilePart2) {
        return csrFilePart2.split("?")[0] || ''
      }
    }
    return ''
  }

  getEventTypeByRole(){
    let userRole = localStorage.getItem("USER_ROLE").replace(/^"|"$/g, '');
    switch (userRole) {
      case "po":
        return "Approve e-Form Request to PO";
      case "ba":
        return "Approve e-Form Request to BA/SA";
      case "ea":
        return "Approve e-Form Request to EAPI";
      case 'km':
        return"Approve e-Form Request to Key Manager";
    }
  }
}
