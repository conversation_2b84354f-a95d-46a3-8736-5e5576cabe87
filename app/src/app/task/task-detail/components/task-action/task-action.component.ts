import { Component, OnInit, Input } from '@angular/core';
import { UntypedFormControl, Validators } from '@angular/forms';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

import { UITextService } from '../../../../shared/services/uitext-service/uitext.service';
import { TaskService } from '../../../services/task-service/task.service';
import { UserService } from 'src/app/shared/services/user-service/user.service';
import { LoadingService } from 'src/app/shared/services/loading-service/loading.service';
import { Router } from '@angular/router';

// Extend Day.js with the relativeTime plugin
dayjs.extend(relativeTime);
@Component({
    selector: 'app-task-action',
    templateUrl: './task-action.component.html',
    styleUrls: ['./task-action.component.less'],
    standalone: false
})
export class TaskActionComponent implements OnInit {
  modalRef: NgbModalRef;
  inputComments: UntypedFormControl;
  authorizedUser = false;
  selectedTab = 'action';
  actionHistoryList = [];
  actionList = [];
  actionStatusIcons = {
    approved: '../../../../../assets/images/task-action-approved.png',
    rejected: '../../../../../assets/images/task-action-rejected.png',
    pending: '../../../../../assets/images/task-action-pending.png',
    disabled: '../../../../../assets/images/task-action-disabled.png'
  };
  currentActionDetails;
  userApprovalGroup;
  decision;
  comments = '';
  isKmRole: boolean;
  showUATDonwloadLink = false;

  @Input() subTask: any;
  @Input() subTaskActionHistory: any;
  @Input() rejectedSubTask: any;
  constructor(
    private modalService: NgbModal,
    private taskService: TaskService,
    private userService: UserService,
    private loadingService: LoadingService,
    private router: Router,
    public labels: UITextService
  ) {}

  ngOnInit() {
    this.isKmRole = false;
    this.inputComments = new UntypedFormControl('', [Validators.required,Validators.minLength(5)]);
    this.makeDecision = this.makeDecision.bind(this);
    this.userService.getUserInfo().subscribe(
      userInfo => {
        if (userInfo) {
          this.userApprovalGroup = userInfo.approvalGroup;
          // Todo: to remove the logic to retrieve the role to disable the approved button
          this.isKmRole = userInfo.approvalGroup === 'km';
        }
        this.generateActionHistoryList();
        this.generateActionList();
      },
      err => {
        const message = this.labels.current.UNEXPECTED_SERVER_ERROR;
        this.loadingService.showError(message, err.message);
      }
    );
  }

  onTabSelect(tab) {
    this.selectedTab = tab;
  }

  generateActionHistoryList() {
    this.actionHistoryList = this.subTaskActionHistory.map((actionHistory, index) => {
      if (index === this.subTaskActionHistory.length - 1 || actionHistory.statusName === 'pending_checker_review') {
        return {
          ...actionHistory,
          assignment_description: `BU ${actionHistory.action}`,
          action_text: `${actionHistory.actorEmail} Request ${this.subTask.environment} Key`,
          statusIcon: this.actionStatusIcons.approved
        };
      }
      if (actionHistory.statusName === 'pending_business_review' ) {
        return {
          ...actionHistory,
          assignment_description: 'BU Team has approved ticket',
          action_text: `${actionHistory.actorEmail} ${actionHistory.action} ticket`,
          statusIcon: this.actionStatusIcons.approved
        };
      }
      const previousActionStatus = this.subTaskActionHistory[index + 1].statusName;
      let actionTemplateData = this.taskService.actionListTemplate.find(actionTemplate => actionTemplate.status === previousActionStatus);
      if (!actionTemplateData) {
        const actionTemplateIndex = this.taskService.actionListTemplate.findIndex(
          actionTemplate => actionTemplate.status === actionHistory.statusName
        );
        actionTemplateData = this.taskService.actionListTemplate[actionTemplateIndex - 1];
      }
      if (!actionTemplateData) {
        return null;
      }
      return {
        ...actionHistory,
        assignment_description: actionTemplateData.assignment_description,
        action_text: `${actionHistory.actorEmail} ${actionHistory.action}`,
        statusIcon: actionHistory.action === 'Approved' ? this.actionStatusIcons.approved : this.actionStatusIcons.rejected
      };
    });
  }

  generateActionList() {
    let isCurrentActionFound = false;
    let isRejectActionFound = false;
    this.actionList = this.taskService.actionListTemplate.map((actionTemplate, currentIndex) => {
      if (isCurrentActionFound) {
        return {
          ...actionTemplate,
          currentAction: false,
          disabledAction: true,
          statusIcon: this.actionStatusIcons.disabled
        };
      }
      const actionHistory = this.subTaskActionHistory.find(el => {
        if (this.taskService.actionListTemplate[currentIndex + 1]) {
          return el.statusName === this.taskService.actionListTemplate[currentIndex + 1].status;
        }
        return null;
      });
      if (actionTemplate.status === this.subTask.subTaskStatusName) {
        isCurrentActionFound = true;
        this.showUATDonwloadLink = this.subTask.environment === 'PROD' && this.subTask.general_section.testFileUrl;
        this.authorizedUser = this.userApprovalGroup && this.taskService.approvalGroupMapping[actionTemplate.action_event] === this.userApprovalGroup;
        this.currentActionDetails = {
          ...actionTemplate,
          ...actionHistory,
          currentAction: true,
          disabledAction: false,
          statusIcon: this.actionStatusIcons.pending,
          nextStatus: this.taskService.actionListTemplate[currentIndex + 1]
        };
        return this.currentActionDetails;
      }
      if (this.rejectedSubTask && !actionHistory) {
        if (!isRejectActionFound) {
          const rejectionAction = this.subTaskActionHistory.find(el => {
            isRejectActionFound = true;
            return this.taskService.closedTicketStatus.includes(el.statusName);
          });
          return {
            ...actionTemplate,
            ...rejectionAction,
            currentAction: false,
            disabledAction: false,
            statusIcon: this.actionStatusIcons.rejected
          };
        }
        return {
          ...actionTemplate,
          actorEmail: '-',
          currentAction: false,
          disabledAction: false
        };
      }
      return {
        ...actionTemplate,
        ...actionHistory,
        currentAction: false,
        disabledAction: false,
        statusIcon: this.actionStatusIcons.approved
      };
    });
    this.actionList.pop();
  }

  formatDate(date) {
    if (!date) {
      return '';
    }
    const momentObj = dayjs(date);
    return `${momentObj.format('DD/MM/YYYY')} (${momentObj.fromNow()})`;
  }

  makeDecision(decision, content) {
    this.loadingService.showLoading();
    this.decision = decision;
    const decisionPayload = {
      actionEvent: decision === 'approved' ? this.currentActionDetails.action_event : decision,
      comment: this.comments,
      userId: this.subTask.general_section.updatedUserId,
      applicationName: this.subTask.general_section.applicationName
    };
    this.taskService.updateSubTask(this.subTask.ticketSubTaskUuid, decisionPayload, decision).subscribe(
      response => {
        this.loadingService.hideLoading();
        if (response.success) {
          this.openModal(content, null);
        } else {
          this.loadingService.showError(response.title, response.message);
        }
      },
      err => {
        this.loadingService.hideLoading();
        const message = this.labels.current.UNEXPECTED_SERVER_ERROR;
        this.loadingService.showError(message, err.message);
      }
    );
  }

  openModal(content, size) {
    this.modalRef = this.modalService.open(content, { centered: true, size });
  }

  closeModal() {
    this.inputComments.patchValue('');
    this.modalRef.close();
  }

  closeModalAndRedirect() {
    this.closeModal();
    this.router.navigate(['task']);
  }

  openCommentsModal(decision, content) {
    this.decision = decision;
    this.openModal(content, 'lg');
  }

  getCommentsModalTitle() {
    if (this.decision === 'rejected_need_more_info') {
      return 'Please input the comment to request for more information.';
    }
    return 'Please input the reason to reject';
  }

  getTicketDecisionLabel() {
    return this.decision === 'approved' ? 'approved' : 'rejected';
  }

  getTicketDecisionStatus() {
    switch (this.decision) {
      case 'approved': {
        return `"${this.currentActionDetails.nextStatus.pending_text}"`;
      }
      case 'rejected_close': {
        return `"Rejected" as closed`;
      }
      case 'rejected_need_more_info': {
        return `"Rejected" as need more info`;
      }
      default:
        return '';
    }
  }

  submitComments(modalContent) {
    this.comments = this.inputComments.value;
    this.closeModal();
    this.makeDecision(this.decision, modalContent);
  }

  downloadFile() {
    window.open(this.subTask.general_section.testFileUrl, '_blank');
  }
}
