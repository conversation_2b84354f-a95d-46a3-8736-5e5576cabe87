import { Component, OnInit } from '@angular/core';
import { ApplicationService } from '../services/application-service/application.service';
import { LoadingService } from '../../shared/services/loading-service/loading.service';
import { UITextService } from '../../shared/services/uitext-service/uitext.service';
import tableHeadersArray from './table-headers.json';

@Component({
    selector: 'app-app-list',
    templateUrl: './app-list.component.html',
    styleUrls: ['./app-list.component.less'],
    standalone: false
})

export class AppListComponent implements OnInit {
  tableHeaders = tableHeadersArray;
  totalRecord = 0;
  appLists = [];
  pageSize = 10;
  page = 1;
  sizeOptions = [10, 20, 50, 100];
  debounce: any;
  sortBy = 'applicationName';
  isAscOrdered = true; // default sorting is from ascending
  paginationLabel = '';
  keyword = '';

  constructor(private applicationService: ApplicationService, private loading: LoadingService, public labels: UITextService) { }

  ngOnInit() {
    this.loading.showLoading();
    this.getApplication();
  }

  // filterList() {
  //   if (this.keyword.length >= 3) {
  //     return this.appLists.filter(app => {
  //       return (
  //         app.uuid.includes(this.keyword) ||
  //         app.name.includes(this.keyword) ||
  //         app.organizationName.includes(this.keyword) ||
  //         app.createdAt.includes(this.keyword)
  //       );
  //     });
  //   } else {
  //     return this.appLists;
  //   }
  // }

  calcOffsetLimit() {
    const offset = (this.page - 1) * this.pageSize;
    return {
      offset,
      limit: this.pageSize
    };
  }

  debounceGetApplication() {
    clearTimeout(this.debounce);
    this.debounce = setTimeout(() => {
      this.getApplication();
    }, 100);
  }

  onPageChange() {
    this.debounceGetApplication();
  }

  sort(fieldName) {
    if (this.sortBy === fieldName) {
      this.isAscOrdered = !this.isAscOrdered;
    } else {
      this.isAscOrdered = true;
    }

    this.sortBy = fieldName;
    this.debounceGetApplication();
  }

  selectPageSize(sizeOption) {
    this.pageSize = sizeOption;
    this.debounceGetApplication();
  }

  isDropdownDisabled() {
    return this.totalRecord < this.sizeOptions[0];
  }

  genPaginationLabel() {
    let startIndex = this.pageSize * (this.page - 1) + 1;
    const lastIndex = startIndex + this.appLists.length - 1;

    if (this.appLists.length === 0) { startIndex = 0; }
    this.paginationLabel = `Showing ${startIndex}-${lastIndex} of ${this.totalRecord}`;
  }

  getApplication() {
    const { offset, limit } = this.calcOffsetLimit();

    const params = {
      offset,
      limit,
      sortBy: this.sortBy,
      sortDesc: this.isAscOrdered ? 'ASC' : 'DESC'
    };

    this.loading.showLoading();
    this.applicationService.getApplicationLists(params).subscribe(
      result => {
        this.loading.hideLoading();
        if (result.data) {
          const { totalRecord, applications: appLists } = result.data;
          this.totalRecord = totalRecord;
          this.appLists = appLists;
          this.genPaginationLabel();
        }
      },
      err => {
        this.loading.hideLoading();
        this.loading.showError(this.labels.current.TASK_LIST_ERROR_TITLE, this.labels.current.TASK_LIST_ERROR_DETAIL);
      }
    );
  }

  generateHeaderSortedId(field) {
    return field ? 'app-list-header-' + field.toLowerCase().split(' ').join('-') : '';
  }

  generatePageSizeOptionId(sizeOption) {
    return sizeOption ? 'app-list-display-item-' + sizeOption : '';
  }

}
