import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ApplicationService } from '../services/application-service/application.service';
import { LoadingService } from 'src/app/shared/services/loading-service/loading.service';
import { UITextService } from 'src/app/shared/services/uitext-service/uitext.service';

@Component({
    selector: 'app-app-detail',
    templateUrl: './app-detail.component.html',
    styleUrls: ['./app-detail.component.less'],
    standalone: false
})
export class AppDetailComponent implements OnInit {

  appId: string;
  appName: string;
  orgName: string;
  industry: string;
  scopes = {
    existScopes: null,
    newScopes: null
  };
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private applicationService: ApplicationService,
    private loading: LoadingService,
    private labels: UITextService
  ) {}

  ngOnInit() {
    this.appId = this.route.snapshot.paramMap.get('appId');
    this.getAppInfo();
  }

  getAppInfo() {
    this.loading.showLoading();
    this.applicationService.getApplicationInfo(this.appId).subscribe(result => {
      this.loading.hideLoading();
      if (result.data && result.data.subTasks) {
        result = result.data.subTasks[0];
        this.appName = this.getApplicationName(result.taskName);
        this.orgName = result.organizationName;
        this.industry = result.industryName || '-';
        this.validateScopes(result.scopes);
      }
    },
      err => {
        this.loading.hideLoading();
        this.loading.showError(this.labels.current.ERROR, this.labels.current.NOT_FOUND_ERROR).then(() => this.router.navigate(['application']));
      }
    );
  }

  getApplicationName(ticketName) {
    return ticketName.replace(/\[NEW\] |\[EDIT\] | -| UAT| PROD/g, '');
  }

  validateScopes(scopes) {
    try {
      scopes = JSON.parse(scopes);
      this.scopes.newScopes = scopes;
    } catch (err) {
      this.scopes.existScopes = scopes;
    }
  }
}
