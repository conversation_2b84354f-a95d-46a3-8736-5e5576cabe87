import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

import { UITextService } from "src/app/shared/services/uitext-service/uitext.service";
import { LoadingService } from "src/app/shared/services/loading-service/loading.service";

import { EmailVerifyService } from '../services/enrollment-verify/email-verify.service';

@Component({
    selector: 'app-enrollment-result',
    templateUrl: './enrollment-result.component.html',
    styleUrls: ['./enrollment-result.component.less'],
    standalone: false
})
export class EnrollmentResultComponent implements OnInit {
  status: boolean;
  imageIcon: string;
  resultTitle: string;
  resultMessage: string;
  isHidden: boolean = true;
  verifyToken: string;

  constructor(
    private verifyService: EmailVerifyService,
    private loading: LoadingService,
    private router: Router,
    private route: ActivatedRoute,
    public labels: UITextService
  ) {}

  ngOnInit() {
    this.verifyToken = this.route.snapshot.paramMap.get('verifyToken');
    this.verifyEmailToDisplay(this.verifyToken);
  }

  verifyEmailToDisplay(verifyToken: string) {
    this.loading.showLoading();
    //call API to verify
    this.verifyService.verifyEmail(verifyToken).subscribe((response) => {
      this.loading.hideLoading();
      this.isHidden = false;
      // Map Display success or fail
      if (response.success === true) {
        this.imageIcon = 'assets/images/icon-success.png';
        this.resultTitle = this.labels.current.VERIFY_EMAIL_TITLE_SUCCESS;
        this.resultMessage = this.labels.current.VERIFY_EMAIL_MESSAGE_SUCCESS;
      } else {
        this.imageIcon = 'assets/images/icon-cancel.png';
        this.resultTitle = this.labels.current.VERIFY_EMAIL_TITLE_INVALID;
        this.resultMessage = this.labels.current.VERIFY_EMAIL_MESSAGE_INVALID;
      }
    });
  }

  backToHome() {
    this.router.navigate(['enrollment']);
  }
}
