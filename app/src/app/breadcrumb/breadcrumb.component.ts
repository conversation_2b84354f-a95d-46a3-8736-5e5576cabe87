import { Component, OnInit, Input } from '@angular/core';
import { NavigationEnd, ActivatedRoute, Router } from '@angular/router';
import { UITextForMigratingService } from '../services/uitext/uitext.service';

@Component({
    selector: 'app-breadcrumb',
    templateUrl: './breadcrumb.component.html',
    styleUrls: ['./breadcrumb.component.less'],
    standalone: false
})
export class BreadcrumbComponent implements OnInit {

  @Input() id: string;

  breadcrumbs: BreadCrumb[];

  constructor(private activatedRoute: ActivatedRoute, private router: Router, public labels: UITextForMigratingService) {
    this.router.events.forEach(event => {
      if (event instanceof NavigationEnd) {
        this.breadcrumbs = this.buildBreadCrumb(this.activatedRoute.root);
      }
    });
  }

  ngOnInit() {
  }

  buildBreadCrumb(activatedRoute: ActivatedRoute, url: string = '', breadcrumbs: Array<BreadCrumb> = []): Array<BreadCrumb> {
    let queryParams;
    activatedRoute.params.subscribe(result => queryParams = result);
    const label = activatedRoute.routeConfig && activatedRoute.routeConfig.data ? activatedRoute.routeConfig.data.breadcrumb : '';
    const linkable = activatedRoute.routeConfig && activatedRoute.routeConfig.data ? activatedRoute.routeConfig.data.breadcrumb : false;
    const path = activatedRoute.routeConfig ? (queryParams[activatedRoute.routeConfig.path.replace(':', '')] || activatedRoute.routeConfig.path) : '';
    const nextUrl = `${url}${path}/`;
    const breadcrumb = {
      label,
      url: nextUrl,
      linkable
    };
    const newBreadcrumbs = breadcrumb.label ? [...breadcrumbs, breadcrumb] : breadcrumbs;
    if (activatedRoute.firstChild) {
      return this.buildBreadCrumb(activatedRoute.firstChild, nextUrl, newBreadcrumbs);
    }
    return newBreadcrumbs;
  }
}

interface BreadCrumb {
  label: string;
  url: string;
}
