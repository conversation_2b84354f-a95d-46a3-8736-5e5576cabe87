import { Component, OnInit, ViewChild } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { LoadingService } from 'src/app/shared/services/loading-service/loading.service';
import { UITextService } from 'src/app/shared/services/uitext-service/uitext.service';
import { PopUpBoxComponent } from '../popup-box/popup-box.component';
import { CertService } from '../services/cert-service/cert.service'
import { UserService } from 'src/app/shared/services/user-service/user.service';
import tableStructure from './table-headers.json';
@Component({
    selector: 'app-cert-list',
    templateUrl: './cert-list.component.html',
    styleUrls: ['./cert-list.component.less'],
    standalone: false
})
export class CertListComponent implements OnInit {

  @ViewChild(PopUpBoxComponent, {static: true}) popUpBox: PopUpBoxComponent;
  modalRef: NgbModalRef;
  tableStructure = tableStructure;
  totalRecords: number = 0;
  certificates = [];
  sizeOptions = [10, 20, 50, 100];
  rowId: string;
  pageSize = 10;
  page = 1;
  sortBy = 'createdAt';
  debounce: any;
  isAscOrdered = false;
  showIndex = false;
  paginationLabel = '';
  userApprovalGroup;
  actionEvent: string = '';
  currentCertificateTicketNo: string = '';
  certificateTicketStatus: string = '';
  

  constructor(
    private loading: LoadingService,
    public labels: UITextService,
    private certService : CertService,
    private userService : UserService,
    private modalService: NgbModal,
  ) {}

  ngOnInit() {
    this.getCertificate();
    this.userService.getUserInfo().subscribe(
        userInfo => {
          this.userApprovalGroup = userInfo.approvalGroup;
        }
    );
  }

  calcOffsetLimit() {
    const offSet = (this.page - 1) * this.pageSize;
    return {
      offSet,
      limit: this.pageSize
    };
  }

  getCertificate(){
    const { offSet, limit } = this.calcOffsetLimit();
    const params = {
      offSet,
      limit,
      sortBy: this.sortBy,
      sortDesc: this.isAscOrdered ? 'ASC' : 'DESC'
    };
    this.loading.showLoading();
    this.certService.getCertificateByRole(params).subscribe(
      result => {
        this.loading.hideLoading();
        if (result.data) {
          const { totalRecord, certificateTickets } = result.data;
          this.totalRecords = totalRecord;
          this.certificates = certificateTickets;
          this.genPaginationLabel();
        }
      },
      err => {
        this.loading.hideLoading();
        this.loading.showError(this.labels.current.TASK_LIST_ERROR_TITLE, this.labels.current.TASK_LIST_ERROR_DETAIL);
      }
    );
  }

  shouldDisabled() {
    return this.totalRecords < this.sizeOptions[0];
  }
  
  genPaginationLabel() {
    let startIndex = this.pageSize * (this.page - 1) + 1;
    const lastIndex = startIndex + this.certificates.length - 1;

    if (this.certificates.length === 0) { startIndex = 0; }
    this.paginationLabel = `Showing ${startIndex}-${lastIndex} of ${this.totalRecords}`;
  }

  debounceGetCertificate() {
    clearTimeout(this.debounce);
    this.debounce = setTimeout(() => {
      this.getCertificate();
    }, 100);
  }
    
  onPageChange() {
    this.debounceGetCertificate();
  }

  sort(fieldName) {
    if (this.sortBy === fieldName) {
      this.isAscOrdered = !this.isAscOrdered;
    } else {
      this.isAscOrdered = true;
    }

    this.sortBy = fieldName;
    this.debounceGetCertificate();
  }

  selectPageSize(sizeOption) {
    this.pageSize = sizeOption;
    this.debounceGetCertificate();
  }

  generatePageSizeOptionId(sizeOption) {
    return sizeOption ? 'certificate-list-display-item-' + sizeOption : '';
  }

  generateHeaderSortedId(field) {
    return field ? 'task-list-header-' + field.toLowerCase().split(' ').join('-').replace('.', '') : '';
  }
  
  rejectConfirm(certificateTicket, content) {
    const {certificateUuid, certificateTicketNo } = certificateTicket;
    this.actionEvent = 'rejected';
    this.loading
    .getConfirm(this.labels.current.REGENERATE_REJECT_CONFIRMATION, "")
    .subscribe(isConfirm => {
      if (isConfirm) {
        this.updateCertificate(certificateUuid, 'rejected', content );
        this.currentCertificateTicketNo = certificateTicketNo
      }
    });
  }

  approveConfirm(certificateTicket, content) {
    const {certificateUuid, certificateTicketNo } = certificateTicket;
    this.loading
    .getConfirm(this.labels.current.REGENERATE_APPROVE_CONFIRMATION, "")
    .subscribe(isConfirm => {
      if (isConfirm) {
        this.updateCertificate(certificateUuid, 'approve', content);
        this.currentCertificateTicketNo = certificateTicketNo
      }
    });
  }

  onClick(content, organizationName) {
    this.popUpBox.visible = false;
    this.popUpBox.content = content;
    this.popUpBox.organizationName = organizationName;
  } 

  generateConfirmActionEventByRole(userApprovalGroup): string{
    switch (userApprovalGroup) {
      case 'po':
       return 'business_approved';
      case 'ba':
        return 'technical_approved';
      case 'ea': 
        return 'ea_approved';
      case 'km':
        return 'key_generated';
    }
  }

  updateCertificate(certificateUuid, decision, content){
    this.actionEvent =  decision === 'rejected' ? 'rejected':this.generateConfirmActionEventByRole(this.userApprovalGroup) ;
    this.loading.showLoading();
    this.certService.updateCertificate(certificateUuid, this.actionEvent).subscribe(
      response => {
        this.loading.hideLoading();
        if (response.success) {
          this.openModal(content, null);
        } else {
          this.loading.showError(response.title, response.message);
        }
      },
      err => {
        this.loading.hideLoading();
        const message = this.labels.current.UNEXPECTED_SERVER_ERROR;
        this.loading.showError(message, err.message);
      }
    );
  }
  openModal(content, size) {
    this.modalRef = this.modalService.open(content, { centered: true, size });
  }

  closeModal() {
    this.modalRef.close();
    this.getCertificate();
  }

  getCertificateActionLabel() {
    switch(this.actionEvent) {
      case 'business_approved':
        this.certificateTicketStatus = 'Pending Technical Review ';
        return 'Approved';
      case 'technical_approved':
        this.certificateTicketStatus = 'Pending Enterprise Review ';
        return 'Approved';
      case 'ea_approved':
        this.certificateTicketStatus = 'Pending Key Manager Generate Certificate';
        return 'Approved';
      case 'key_generated':
        this.certificateTicketStatus = 'Closed';
        return 'Approved';
      case 'rejected':
        this.certificateTicketStatus = 'Rejected';
        return 'Rejected';
    }
  }

  getRowId(row) {
    return row[this.rowId];
  }

}
