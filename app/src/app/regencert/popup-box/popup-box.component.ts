import { Component, Input, OnInit } from '@angular/core';

@Component({
    selector: 'app-pop-up-box',
    templateUrl: './popup-box.component.html',
    styleUrls: ['./popup-box.component.less'],
    standalone: false
})
export class PopUpBoxComponent implements OnInit {
  @Input() visible: boolean = true;
  @Input() content: string;
  @Input() organizationName: string;
  constructor() { }

  ngOnInit() {
  }

  onClose() {
    this.visible = true;
  }

}
