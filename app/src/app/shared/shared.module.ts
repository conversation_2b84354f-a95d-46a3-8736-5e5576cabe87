import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { RouterModule } from '@angular/router';
import { HeaderComponent } from './components/header/header.component';
import { SidebarComponent } from './components/sidebar/sidebar.component';
import { LoadingLayerComponent } from './components/loading-layer/loading-layer.component';
import { NotFoundComponent } from './components/not-found/not-found.component';
import { AppScopesComponent } from './components/app-scopes/app-scopes.component';
import { NgbAccordionModule } from '@ng-bootstrap/ng-bootstrap';

@NgModule({
  declarations: [
    HeaderComponent,
    SidebarComponent,
    LoadingLayerComponent,
    NotFoundComponent,
    AppScopesComponent
  ],
  imports: [
    NgbModule,
    CommonModule,
    RouterModule,
    NgbAccordionModule
  ],
  exports: [
    HeaderComponent,
    SidebarComponent,
    LoadingLayerComponent,
    NotFoundComponent,
    AppScopesComponent
  ]
})
export class SharedModule { }
