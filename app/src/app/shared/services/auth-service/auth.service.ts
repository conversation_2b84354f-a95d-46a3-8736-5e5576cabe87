import { Injectable, Inject } from "@angular/core";
import { Observable, of, throwError } from "rxjs";
import { switchMap, catchError } from "rxjs/operators";
import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";
// import config from "../../../../config";
import { Headers } from "../../models/headers";
import { Token } from "../../models/token";

import { MsalBroadcastService } from "@azure/msal-angular";
import {
  PublicClientApplication,
  AuthenticationResult,
  InteractionStatus,
} from "@azure/msal-browser";
import { filter } from "rxjs/operators";

const currentUrl = window.location.hostname; //https://developer-admin-dev.se.scb.co.th
let env = "local";
if (currentUrl === "developer-admin-dev.se.scb.co.th") env = "dev";
else if (currentUrl === "developer-admin-sit.se.scb.co.th") env = "sit";
else if (currentUrl === "developer-admin-uat.se.scb.co.th") env = "uat";
else if (currentUrl === "developer-admin-prod.scb.co.th") env = "prod";
const config = require(`../../../../config/${env}`);

const KEY_AUTH_DATA = "PE_AUTH_DATA";
const PE_ME_TOKEN = "PE_ME_TOKEN";

@Injectable({
  providedIn: "root",
})
export class AuthService {
  TOKEN_TYPE = "Bearer";
  token: Token;
  idToken: Token;
  headers: Headers;

  resourceOwnerId: string;

  private readonly msalConfig = config.msalConfig;
  private readonly msalInstance: PublicClientApplication;
  public user;
  

  constructor(
    // private keycloakAngular: KeycloakService,
    private http: HttpClient,
    private msalBroadcastService: MsalBroadcastService,
    
  ) {
    this.msalInstance = new PublicClientApplication(this.msalConfig);
  }

  cachedTokenTimeout: any = 0;

  userInfo() {
    return this.user;
  }

  loginPopup() {
    return this.msalInstance
      .loginPopup({
        scopes: ["user.read"],
      })
      .then((response) => {
        this.user = response;
        this.setTokenIntoStorage({ accessToken: response.accessToken });
        this.setIdTokenIntoStorage({ accessToken: response.idToken });
      });
  }

  login(): any {
    this.msalBroadcastService.inProgress$
      .pipe(
        filter((status: InteractionStatus) => {
          return status === InteractionStatus.Startup;
        })
      )
      .subscribe(async () => {
        const result = await this.handleRedirect();
        const acc = result ? result.account : null;
        this.token = JSON.parse(localStorage.getItem(KEY_AUTH_DATA));


        if (!acc)
          this.msalInstance
            .loginRedirect({
              scopes: ["user.read"],
            })
            .then((response) => {
              console.log("Login process initiated");
            });

        this.user = await this.handleRedirect();

        // this.checkToken();
        if (this.user && !this.token) {
          this.setTokenIntoStorage({ accessToken: this.user.accessToken });
          this.setIdTokenIntoStorage({ accessToken: this.user.idToken });
        }
        return this.user;
      });
  }

  async checkToken() {
    // const accessToken = this.msalInstance.getAllAccounts()[0].idToken;
    const result = await this.handleRedirect();
    const accessToken = result ? result.accessToken : "";
    if (accessToken) {
      fetch("https://graph.microsoft.com/v1.0/me", {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      })
        .then((response) => response.json())
        .then((data) => console.log("User data:", data))
        .catch((error) => console.error(error));
    } else {
      console.log("No access token available");
    }
  }

  handleRedirect(): Promise<AuthenticationResult | null> {
    return this.msalInstance.handleRedirectPromise();
  }

  logout(): Promise<void> {
    localStorage.removeItem(KEY_AUTH_DATA)
    localStorage.removeItem(PE_ME_TOKEN)
    localStorage.removeItem("CHECK_EXISTED_USER")
    localStorage.removeItem("ORGANIZATION_UUID")
    localStorage.removeItem("USER_ROLE")
    localStorage.removeItem("USER_INFO")
    localStorage.removeItem("IS_USER")
    localStorage.removeItem("IS_MANAGER")
    localStorage.removeItem("IS_ADMIN")
    return this.msalInstance.logoutRedirect({});
    // return this.msalInstance.logoutPopup({});
  }

  getUsername(): string | null {
    const account = this.msalInstance.getActiveAccount();
    return account ? account.username : null;
  }

  //--------------------------------------------------------------------------

  // https://stackoverflow.com/questions/105034/create-guid-uuid-in-javascript
  generateUUID(): string {
    let d = new Date().getTime();
    if (
      typeof performance !== "undefined" &&
      typeof performance.now === "function"
    ) {
      // use high-precision timer if available
      d += performance.now();
    }
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c === "x" ? r : (r & 0x3) | 0x8).toString(16);
    });
  }

  generateHeaders(idToken: string): Headers {
    return {
      resourceOwnerId: "admin-portal",
      requestUId: this.generateUUID(),
      authorization: `${this.TOKEN_TYPE} ${idToken}`
    } as Headers;
  }

  setTokenIntoStorage(token: Token): void {
    this.token = token;
    localStorage.setItem(KEY_AUTH_DATA, JSON.stringify(token));
  }

  setIdTokenIntoStorage(token: Token): void {
    localStorage.setItem(PE_ME_TOKEN, JSON.stringify(token));
  }


  validateTokens(): Observable<boolean> {
    return this.http.get(`${environment.apiDomain}/api/token/validate`).pipe(
      switchMap((res: any) => {
        if (res && res.data) {
          // after call token validate update cachedTokenTimeout for 15 sec.
          this.cachedTokenTimeout = Math.floor(Date.now() / 1000) + 15;
          return of(true);
        } else {
          throwError(new Error("invalid token"));
        }
      }),
      catchError((error) => {
        throw new Error(error);
      })
    );
  }

  // getTokenFromKeycloak(): Observable<Token> {
  //   return new Observable((observer) => {
  //     this.keycloakAngular
  //       .getToken()
  //       .then((accessToken) => {
  //         const token = {
  //           accessToken,
  //         };
  //         this.setTokenIntoStorage(token);
  //         return observer.next(token);
  //       })
  //       .catch((err) => {
  //         throw new Error(err);
  //       });
  //   });
  // }

  // getToken(): Observable<Token> {
  //   const currentTime = Math.floor(Date.now() / 1000);
  //   // if this service have a token and currentTime more than cachedTokenTimeout value call validateToken() otherwise assume current token is valid.
  //   return (
  //     this.token && this.cachedTokenTimeout < currentTime
  //       ? this.validateTokens()
  //       : of(true)
  //   ).pipe(
  //     switchMap((isValidToken) =>
  //       isValidToken
  //         ? this.getTokenFromKeycloak()
  //         : throwError(new Error("invalid token"))
  //     ),
  //     catchError((error) => {
  //       throw new Error(error);
  //     })
  //   );
  // }

  getHeaders(hasCachedToken: boolean = false): Observable<Headers> {
    this.token = JSON.parse(localStorage.getItem(KEY_AUTH_DATA));
    this.idToken = JSON.parse(localStorage.getItem(PE_ME_TOKEN))

    return new Observable((observer) => {
      if (hasCachedToken && this.token && this.token.accessToken) {
        const token = JSON.parse(JSON.stringify(this.token));
        const idToken = JSON.parse(JSON.stringify(this.idToken));
        // this.setTokenIntoStorage(null);
        return observer.next(this.generateHeaders(idToken.accessToken));
      } else {
        // this.getToken().subscribe(
        //   (token) => {
        return observer.next(this.generateHeaders(this.idToken.accessToken));
        // },
        // (err) => {
        //   throw new Error(err);
        // }
        // );
      }
    });
  }

  // getUserRoles = () => this.keycloakAngular.getUserRoles();

  // logout = () => {
  //   this.setTokenIntoStorage(null);
  //   this.keycloakAngular.logout();
  // };
}
