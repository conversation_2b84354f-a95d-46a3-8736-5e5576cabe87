import { Injectable } from "@angular/core";
import {
  HttpClient,
  HttpErrorResponse,
  HttpParams,
} from "@angular/common/http";
import { Observable, of } from "rxjs";
import { catchError, map, mergeMap } from "rxjs/operators";
import { AuthService } from "../auth-service/auth.service";
import { ConfigService } from "../config-service/config.service";
import { ErrorService } from "../error-service/error.service";

import { LoadingService } from "../loading-service/loading.service";
import { EMPTY } from "rxjs";
import { UITextService } from "../uitext-service/uitext.service";
import { jwtDecode } from "jwt-decode";


import { Inject } from "@angular/core";


export const UserRoles = {
  APP_USER: "partnereco_admin_portal_app_user",
  APP_ADMIN: "partnereco_admin_portal_app_admin",
  APP_MANAGER: "partnereco_admin_portal_app_manager",
};

const rolesAdmin = ["po", "ba", "ea", "km"];
const rolesManager = ["pm"];
const rolesUser = ["bu"]

const USER_INFO = "USER_INFO";
const IS_ADMIN = "IS_ADMIN"
const IS_MANAGER = "IS_MANAGER"
const IS_USER = "IS_USER"

@Injectable({
  providedIn: "root",
})
export class UserService {
  userInfo;
  isAdmin = false;
  isManager = false;
  isUser = false

  constructor(
    private http: HttpClient,
    private config: ConfigService,
    private errorService: ErrorService,
    protected authService: AuthService,
    private loading: LoadingService,
    public labels: UITextService,
    

  ) {

    const userInfoStorage = localStorage.getItem(USER_INFO)
    const isAdminStorage = localStorage.getItem(IS_ADMIN) === "true"
    const isManagerStorage = localStorage.getItem(IS_MANAGER) === "true"
    const isUserStorage = localStorage.getItem(IS_USER) === "true"
    if(userInfoStorage)
    {
      this.userInfo = userInfoStorage
    }
    
    if(isAdminStorage)
    {
      this.isAdmin = isAdminStorage
    }

    if(isManagerStorage)
    {
      this.isManager = isManagerStorage
    }

    if(isUserStorage)
    {
      this.isUser = isUserStorage
    }
  }

  getUserInfo(flagToStampDateLastLogon: string = "0"): Observable<any> {
    if (this.userInfo) {
      return of(this.userInfo);
    }
    const getLocalStorage = JSON.parse(localStorage.getItem("PE_ME_TOKEN")).accessToken;
    let authUserAccountUsername = ""

    if(this.authService.user && this.authService.user.account.username)
    {
      authUserAccountUsername = this.authService.user.account.username
    }
    else
    {
      const decodeToken:any = jwtDecode(getLocalStorage);
      authUserAccountUsername = decodeToken.preferred_username
    }

 
    if (!this.authService.user && this.userInfo) return this.userInfo;
   
    const email = authUserAccountUsername;
    let request;
    if (flagToStampDateLastLogon === "1") {
      request = this.http.post<any>(
        `${this.config.current.apiDomain}/partners/v1/portal/tickets/approvalGroup`,
        {}
      );
    } else {
      const params = new HttpParams().set("email", email);
      request = this.http.get<any>(
        `${this.config.current.apiDomain}/partners/v1/portal/tickets/approvalGroup`,
        { params }
      );
    }

    return request.pipe(

      map((approvalGroupResult: any) => {
        this.userInfo = approvalGroupResult.data;
        this.userInfo.family_name = approvalGroupResult.data.lastname;
        this.userInfo.given_name = approvalGroupResult.data.firstname;
        this.userInfo.oc_code = approvalGroupResult.data.ocCode;
        this.userInfo.approvalGroup = approvalGroupResult.data.role || "";
        this.isAdmin = rolesAdmin.includes(this.userInfo.approvalGroup);
        this.isManager = rolesManager.includes(this.userInfo.approvalGroup);
        this.isUser = rolesUser.includes(this.userInfo.approvalGroup)

        localStorage.setItem(USER_INFO, this.userInfo)
        localStorage.setItem(IS_ADMIN, this.isAdmin.toString())
        localStorage.setItem(IS_MANAGER, this.isManager.toString())
        localStorage.setItem(IS_USER, this.isUser.toString())

        return this.userInfo;
      }),
      catchError((error: HttpErrorResponse) => {
        console.log("error: ", error);
        const errorActionResponse =
          this.errorService.getErrorActionResponse(error);

        if (
          error &&
          error.error &&
          error.error.status &&
          error.error.status.code === 1104
        ) {
          setTimeout(() => {
            this.loading
              .showError(
                this.labels.current.ERROR,
                this.labels.current.USER_NOT_FOUND_IN_SYSTEM
              )
              .then(() => this.logout(getLocalStorage));
            return EMPTY;
          }, 1000);
        } else {
          setTimeout(() => {
            this.loading
              .showError(
                this.labels.current.ERROR,
                this.labels.current.UNEXPECTED_SERVER_ERROR
              )
              .then(() => this.logout(getLocalStorage));
            return EMPTY;
          }, 1000);
        }
        return of(errorActionResponse);
      })
    );
  }

  getUserTitle(userInfo): string {
    switch (true) {
      case rolesAdmin.includes(userInfo.approvalGroup):
        return "App Administrator";
      case rolesManager.includes(userInfo.approvalGroup):
        return "App Manager";
      default:
        return "App User";
    }
  }

  logout(token): void {
    if(token)
    {
      let request = this.http.post<any>(
        `${this.config.current.apiDomain}/logout`,
        {
          accessToken: token,
        }
      );
      request.subscribe();
    }

    this.authService.logout();
  }

}
