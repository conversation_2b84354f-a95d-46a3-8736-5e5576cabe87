import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { LoadingService } from '../../services/loading-service/loading.service';
import { LoadingData } from '../../services/loading-service/loading-data';
import { Router } from '@angular/router';
import { UITextService } from '../../services/uitext-service/uitext.service';
import { UserService } from '../../services/user-service/user.service';

@Component({
    selector: 'app-loading-layer',
    templateUrl: './loading-layer.component.html',
    styleUrls: ['./loading-layer.component.less'],
    standalone: false
})
export class LoadingLayerComponent implements OnInit, OnDestroy {
  loadingData: LoadingData;
  loadingSubscription: any;

  constructor(private loading: LoadingService, private router: Router, public labels: UITextService, private userService: UserService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.loading.loadingFlag.subscribe((data: LoadingData) => {
      if (data.type !== 'HIDE_LOADING') {
        this.loadingData = data;
      } else {
        this.loadingData = null;
      }
      this.cdr.detectChanges();
    });
  }

  ngOnDestroy(): void {}

  confirm() {
    let callback;
    if ((this.loadingData.type === 'CONFIRM' || this.loadingData.type === 'DISCARD') && this.loadingData.callback) {
      callback = this.loadingData.callback;
    }
    this.loadingData = null;
    if (callback) {
      callback(true);
    }
  }

  closeDialog() {
    const type = this.loadingData.type;

    if (this.loadingData.title === this.labels.current.SESSION_TIMEOUT) {
      const pe_me_token = localStorage.getItem("PE_ME_TOKEN")
      if(pe_me_token)
      {
        this.userService.logout(JSON.parse(pe_me_token).accessToken);
      }
      else
      {
        this.userService.logout(null);
      }
    } else if (
      (type === 'CONFIRM' || type === 'SUCCESS' || type === 'ERROR' || type === 'DISCARD' || type === 'WARNING') &&
      this.loadingData.callback
    ) {
      this.loadingData.callback(false);
    }

    this.loadingData = null;
  }
}
