import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { UserService } from "../../services/user-service/user.service";

@Component({
    selector: "app-not-found",
    templateUrl: "./not-found.component.html",
    styleUrls: ["./not-found.component.less"],
    standalone: false
})
export class NotFoundComponent implements OnInit {
  constructor(private router: Router, private userService: UserService) {}

  ngOnInit() {
    setTimeout(() => {
      switch (true) {
        case this.userService.isAdmin:
          return this.router.navigate(["task"]);
        case this.userService.isManager:
          return this.router.navigate(["tracking"]);
        case this.userService.isUser:
          return this.router.navigate(["request-management"]);
        default:
          return this.router.navigate([""]);
      }
    }, 1000);
  }
}
