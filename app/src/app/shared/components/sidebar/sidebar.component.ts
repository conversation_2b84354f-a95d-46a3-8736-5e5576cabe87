import { Component, OnInit, EventEmitter, Output } from "@angular/core";
import { Location } from "@angular/common";
import {
  Router,
  ActivatedRoute,
  RouterLink,
  RouterModule,
} from "@angular/router";
import { UserService } from "../../services/user-service/user.service";

@Component({
    selector: "app-sidebar",
    templateUrl: "./sidebar.component.html",
    styleUrls: ["./sidebar.component.less"],
    standalone: false
})
export class SidebarComponent implements OnInit {
  @Output() collapseEvent = new EventEmitter<boolean>();

  isAdmin = false;
  isManager = false;
  menus = [];
  collapsed = false;

  constructor(
    private location: Location,
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService
  ) {}

  ngOnInit() {
    this.userService.getUserInfo().subscribe((userInfo: any) => {
      if (userInfo) {
        this.isAdmin = this.userService.isAdmin;
        this.isManager = this.userService.isManager;
      }
      if (this.isAdmin) {
        this.menus.push({
          label: "API Key Approval",
          navigation: ["task"],
          img: "../../../../assets/images/task-list-grey.png",
        });
        this.menus.push({
          label: "CSR Certificate Approval",
          navigation: ["regencert"],
          img: "../../../../assets/images/cert-grey.png",
        });
        this.menus.push({
          label: "Application Management",
          navigation: ["application"],
          img: "../../../../assets/images/app-management-grey.png",
        });
        this.menus.push({
          label: "API Info Management",
          navigation: ["apiInfo/management"],
          img: "../../../../assets/images/tracking-grey.png",
        });
        if(this.userService.userInfo.role !== "km")
        {
          this.menus.push({
            label: "API Consumption Report",
            navigation: ["apiconsumption/report"],
            img: "../../../../assets/images/tracking-grey.png",
          });
        }
      }

      if (!this.isAdmin)
      {
        this.menus.push({
          label: "Ticket List",
          navigation: ["request-management"],
          img: "../../../../assets/images/task-list-grey.png",
        });
        this.menus.push({
          label: "Organization List",
          navigation: ["organization"],
          img: "../../../../assets/images/tracking-grey.png",
        });
        this.menus.push({
          label: "Enroll Email address",
          navigation: ["enrollment"],
          img: "../../../../assets/images/user-management-grey.png",
        });
        this.menus.push({
          label: "Request API Key",
          navigation: ["request-management/onboarding/add"],
          img: "../../../../assets/images/app-management-grey.png",
        });
        this.menus.push({
          label: "Request CSR Certificate",
          navigation: ["request-management/certificate"],
          img: "../../../../assets/images/cert-grey.png",
        });
      }
      if (this.isManager) {
        this.menus.push({
          label: "Approval Tracking",
          navigation: ["tracking"],
          img: "../../../../assets/images/tracking-grey.png",
        });
        this.menus.push({
          label: "Application Management",
          navigation: ["application"],
          img: "../../../../assets/images/app-management-grey.png",
        });
        this.menus.push({
          label: "API Info Management",
          navigation: ["apiInfo/management"],
          img: "../../../../assets/images/tracking-grey.png",
        });
        this.menus.push({
          label: "API Consumption Report",
          navigation: ["apiconsumption/report"],
          img: "../../../../assets/images/tracking-grey.png",
        });
      }
    });
  }

  renderIcon(imgUrl, navigation) {
    if (this.isActive(navigation)) {
      return imgUrl.replace("grey", "purple");
    }

    return imgUrl;
  }

  onCollapse() {
    this.collapsed = !this.collapsed;
    this.collapseEvent.emit(this.collapsed);
  }

  isActive(navigation) {
    return this.location.path() === `/${navigation}`
  }

  generateSidebarMenuId(field) {
    return field ? `sidebar-${field.toLowerCase().split(" ").join("-")}` : "";
  }
}
