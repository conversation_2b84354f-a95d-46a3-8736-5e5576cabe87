import { Component, OnInit, Input } from '@angular/core';
import { ScopeService } from '../../services/scope-service/scope.service';
import { UITextService } from '../../services/uitext-service/uitext.service';
import { LoadingService } from '../../services/loading-service/loading.service';

@Component({
    selector: 'app-app-scopes',
    templateUrl: './app-scopes.component.html',
    styleUrls: ['./app-scopes.component.less'],
    standalone: true
})
export class AppScopesComponent implements OnInit {
  accordionIdList;
  formattedTaskScopes = [];
  requireConsentMapper = {
    "1": "Yes",
    "0": "No",
    "2": "Always Consent",
  }
  authenticationSection = {
    authenticationType: [],
    isRequireConsent: null,
    applicationCodeNss: "",
    productNameNss: []
  };

  @Input() appScopes: any; // Array of app scopes
  @Input() showAuth: boolean;
  @Input() authenticationType: string;
  @Input() isRequireConsent: string;
  @Input() applicationCodeNss: string;
  @Input() productNameNss: string;
  constructor(private loading: LoadingService, private scopeService: ScopeService, private labels: UITextService) {}

  ngOnInit() {
    this.updateScopes();
    this.serializeAuthenticationData();
  }
  
  serializeAuthenticationData() {
      this.authenticationSection.authenticationType = JSON.parse(this.authenticationType);
      this.authenticationSection.isRequireConsent = this.requireConsentMapper[this.isRequireConsent];
      this.authenticationSection.applicationCodeNss = this.applicationCodeNss;
      if (this.authenticationType.includes('web-authen')) {
        this.authenticationSection.productNameNss = JSON.parse(this.productNameNss);
      } else {
        this.authenticationSection.productNameNss = [null];
      }
  }

  updateScopes() {
    this.loading.showLoading();
    this.scopeService.getScopeRef().subscribe(
      scopeRef => {
        this.loading.hideLoading();
        if (this.appScopes) {
          this.formatScopes(scopeRef);
        }
      },
      err => {
        this.loading.hideLoading();
        const message = this.labels.current.UNEXPECTED_SERVER_ERROR;
        this.loading.showError(message, err.message);
      }
    );
  }

  formatScopes(scopeRef) {
    this.formattedTaskScopes = scopeRef
      .map(scope => {
        /* 1. Get detail list of subscopes */
        const subscopeList = scope.subScopes.filter(subScope => {
          const index = this.appScopes.id.findIndex(scopeId => scopeId === subScope.subScopeId);
          if (index > -1) {
            this.appScopes.id.splice(index, 1);
            this.appScopes.name.splice(index, 1);
            return true;
          }
          return false;
        });
        /* 2. Add subscopes to subscope groups */
        const subScopeGroupList = [];
        subscopeList.forEach(subscope => {
          const subScopeGroupToUpdateIndex = subScopeGroupList.findIndex(subScopeGroup => subScopeGroup.groupName === subscope.groupName);
          if (subScopeGroupToUpdateIndex < 0) {
            subScopeGroupList.push({
              groupName: subscope.groupName,
              subscopes: [subscope]
            });
          } else {
            subScopeGroupList[subScopeGroupToUpdateIndex].subscopes.push(subscope);
          }
        });
        return {
          ...scope,
          subScopes: subScopeGroupList
        };
      })
      .filter(formattedTaskScope => formattedTaskScope.subScopes.length > 0);
    /* 3. Populate list of Accordion Panels */
    this.accordionIdList = this.formattedTaskScopes
      .map((formattedTaskScope, index) => {
        return `accordion-panel-${index}`;
      })
      .toString();
  }

  generateSubScopesElementId(field) {
    return field ? field.split('.').join('-') : '';
  }
}
