import { Component, OnInit, HostListener, ViewChild } from "@angular/core";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";
import { UserService } from "../../services/user-service/user.service";
import { map } from "rxjs/operators";

@Component({
    selector: "app-header",
    templateUrl: "./header.component.html",
    styleUrls: ["./header.component.less"],
    standalone: false
})
export class HeaderComponent implements OnInit {
  @ViewChild("headerDropdown") dropdown: NgbDropdown;
  profile: any = {};
  fullName = "";
  roleTitle = "";

  constructor(protected userService: UserService) {}

  ngOnInit() {
    this.userService.getUserInfo().subscribe((userInfo: any) => {
      if (userInfo) {
        this.profile = userInfo;
        this.fullName = `${this.profile.given_name} ${this.profile.family_name}`;
      }
      if (userInfo.aadGroupName !== undefined && userInfo.aadGroupName !== null) {
        this.roleTitle = userInfo.aadGroupName.trim() === '' ? '-' : userInfo.aadGroupName;
      } else {
        this.roleTitle = this.userService.getUserTitle(userInfo);
      }    
    });
  }

  getDivisionName() {
    if (!this.profile.division || !this.profile.oc_code) {
      return "-";
    }
    return `${this.profile.division.replace(/ Division| Department/g, "")} [${
      this.profile.oc_code.split(" ")[0]
    }]`;
  }

  logout() {
    this.userService.logout(JSON.parse(localStorage.getItem("PE_ME_TOKEN")).accessToken);
    const pe_me_token = localStorage.getItem("PE_ME_TOKEN")
    if(pe_me_token)
    {
      this.userService.logout(JSON.parse(pe_me_token).accessToken);
    }
    else
    {
      this.userService.logout(null);
    }
  }

  @HostListener("window:resize", [])
  onResize() {
    this.dropdown.close();
  }
}
