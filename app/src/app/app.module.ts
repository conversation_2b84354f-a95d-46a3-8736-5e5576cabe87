import { NgModule } from "@angular/core";
import { BrowserModule } from "@angular/platform-browser";
import {
  HttpClientModule,
  HttpClient,
  HTTP_INTERCEPTORS,
} from "@angular/common/http";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { environment } from "./../environments/environment";
import { interceptorFactory } from "./shared/interceptors/interceptor-factory";

import { AppRoutingModule } from "./app-routing.module";
import { SharedModule } from "./shared/shared.module";
import { GenericDropdownModule } from "./generic-dropdown/generic-dropdown.module"
import { IndustryDropdownModule } from "./industry-dropdown/industry-dropdown.module"
import { GeneralInfoModule } from "./onboarding-eform/general-info/general-info.module"
import { ApiServicesModule } from "./onboarding-eform/api-services/api-services.module"
import { SummaryModule } from "./onboarding-eform/summary/summary.module"
import { SummaryInfoModule } from "./onboarding-eform/summary-info/summary-info.module"

import { AppComponent } from "./app.component";
import { LoadingService } from "./shared/services/loading-service/loading.service";
import { UITextService } from "./shared/services/uitext-service/uitext.service";
import { TokenInterceptor } from "./shared/interceptors/token-interceptor";
import { TaskModule } from "./task/task.module";
import { RegenerateCertificateFormModule } from "./regenerate-certificate-form/regenerate-certificate-form.module"
import { CertificateRequestModule } from "./onboarding-eform/certificate-request/certificate-request.module"


import { UITextForMigratingService } from './services/uitext/uitext.service';
import { AnalyticsService } from './services/analytics/analytics.service';
import { ConfigService } from './services/config/config.service';
import { ErrorService } from './services/error/error.service';
import { TicketListService } from './services/ticket-list/ticket-list.service';
import { ApiScopeService } from './services/api-scope/api-scope.service';
import { CountriesService } from './services/countries/countries.service';
import { EmailVerifyService } from './services/enrollment-verify/email-verify.service';
import { OnboardingService } from './services/onboarding/onboarding.service';
import { UserForMigratingService } from './services/user/user.service';
import { ValidatorService } from './services/validator/validator.service';
import { EFormDataMapper } from '../app/models/onboarding-e-form/eformDataMapper'
import { CookieService } from 'ngx-cookie-service';
import { PiiService } from './services/pii/pii.service';
import { ApiConsumptionsMasterDataService } from './services/apiconsumptions-masterdata/apiconsumptions-masterdata.service';
import { ApiManagementService } from './services/apimanagement/apimanagement.service';

import { Angulartics2Module } from 'angulartics2';

import { AppService } from './services/app/app.service'


import {
  MsalModule,
  MsalService,
  MsalGuard,
  MsalInterceptor,
  MsalBroadcastService,
  MsalRedirectComponent,
  MsalInterceptorConfiguration,
  MSAL_GUARD_CONFIG,
  MSAL_INSTANCE,
  MSAL_INTERCEPTOR_CONFIG,
  MsalGuardConfiguration,
} from "@azure/msal-angular";
import {
  IPublicClientApplication,
  PublicClientApplication,
  InteractionType,
  BrowserCacheLocation,
} from "@azure/msal-browser";
import { AuthService } from "./shared/services/auth-service/auth.service";
import { UserService } from "./shared/services/user-service/user.service";
import { Inject } from "@angular/core";
import { IdleService } from "./services/idle/idle.service";


const currentUrl = window.location.hostname; //https://developer-admin-dev.se.scb.co.th
let env = "local";
if (currentUrl === "developer-admin-dev.se.scb.co.th") env = "dev";
else if (currentUrl === "developer-admin-sit.se.scb.co.th") env = "sit";
else if (currentUrl === "developer-admin-uat.se.scb.co.th") env = "uat";
else if (currentUrl === "developer-admin-prod.scb.co.th") env = "prod";

const config = require(`../config/${env}`);

const msalConfig = config.msalConfig;

export function MSALInstanceFactory(): IPublicClientApplication {
  return new PublicClientApplication(msalConfig);
}

export function MSALInterceptorConfigFactory(): MsalInterceptorConfiguration {
  const protectedResourceMap = new Map<string, Array<string>>();
  protectedResourceMap.set("https://graph.microsoft.com/v1.0/me", [
    "user.read",
  ]);

  return {
    interactionType: InteractionType.Redirect,
    protectedResourceMap,
  };
}

export function MSALGuardConfigFactory(): MsalGuardConfiguration {
  return { interactionType: InteractionType.Redirect };
}

@NgModule({
    declarations: [AppComponent],
    imports: [
        BrowserModule,
        MsalModule,
        HttpClientModule,
        AppRoutingModule,
        SharedModule,
        NgbModule,
        TaskModule,
        GenericDropdownModule,
        IndustryDropdownModule,
        GeneralInfoModule,
        ApiServicesModule,
        SummaryModule,
        SummaryInfoModule,
        CertificateRequestModule,
        RegenerateCertificateFormModule
    ],
    providers: [
        {
            provide: MSAL_INSTANCE,
            useFactory: MSALInstanceFactory,
        },
        {
            provide: MSAL_GUARD_CONFIG,
            useFactory: MSALGuardConfigFactory,
        },
        {
            provide: MSAL_INTERCEPTOR_CONFIG,
            useFactory: MSALInterceptorConfigFactory,
        },
        MsalService,
        MsalGuard,
        MsalBroadcastService,
        LoadingService,
        UITextService,
        TokenInterceptor,
        UITextForMigratingService,
        AnalyticsService,
        ConfigService,
        ErrorService,
        TicketListService,
        ApiScopeService,
        CountriesService,
        EmailVerifyService,
        OnboardingService,
        UserForMigratingService,
        ValidatorService,
        EFormDataMapper,
        AppService,
        ValidatorService,
        CookieService,
        PiiService,
        ApiConsumptionsMasterDataService,
        ApiManagementService,
        {
            provide: HTTP_INTERCEPTORS,
            useFactory: interceptorFactory,
            deps: [TokenInterceptor],
            multi: true,
        },
    ]
})
export class AppModule {
  activeUser: any;
  accessToken: any = null
  constructor(
    public authService: AuthService,
    private http: HttpClient,
    private userService: UserService,

  ) {
    const PE_AUTH_DATA = 'PE_AUTH_DATA';

    const accessAuthData = localStorage.getItem(PE_AUTH_DATA)
    if(accessAuthData)
    {
      this.accessToken = JSON.parse(accessAuthData).accessToken
    }
  }

  async ngDoBootstrap(app) {
    if(this.accessToken)
    {
      app.bootstrap(AppComponent)
    }
    else
    {
      this.authService.login();
      const checkRedirectCompleted = await this.authService.handleRedirect();
      if (!!checkRedirectCompleted) {
        await new Promise((res) => setTimeout(res, 0));
        this.userService.getUserInfo("1").subscribe((userInfo) => {
          this.activeUser = userInfo;
          localStorage.setItem("USER_ROLE", userInfo.role);
          if(this.activeUser.success === false){
            localStorage.setItem("CHECK_EXISTED_USER", "false");

          }else{
            localStorage.setItem("CHECK_EXISTED_USER", "true");
          }
          app.bootstrap(AppComponent);
        });
      }
    }
  }
}
