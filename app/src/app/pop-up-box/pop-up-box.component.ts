import { Component, Input, OnInit } from '@angular/core';

@Component({
    selector: 'app-pop-up-box',
    templateUrl: './pop-up-box.component.html',
    styleUrls: ['./pop-up-box.component.less'],
    standalone: false
})
export class PopUpBoxComponentRequestCert implements OnInit {
  @Input() visible: boolean = true;
  @Input() content: string;
  @Input() organizationName: string;
  constructor() { }

  ngOnInit() {
  }

  onClose() {
    this.visible = true;
  }

}
