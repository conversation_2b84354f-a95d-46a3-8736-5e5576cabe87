import { Component, OnInit, ViewChild } from "@angular/core";
import { PopUpBoxComponentRequestCert } from "../pop-up-box/pop-up-box.component";
import { CertificateService } from "../services/certificate/certificate.service";
import { LoadingService } from '../shared/services/loading-service/loading.service';
import { UserService } from '../shared/services/user-service/user.service';
import { UITextForMigratingService } from "../services/uitext/uitext.service";

@Component({
    selector: "app-request-management-certificate-list",
    templateUrl: "./request-management-certificate-list.component.html",
    styleUrls: ["./request-management-certificate-list.component.less"],
    standalone: false
})
export class RequestManagementCertificateListComponent implements OnInit {
  totalRecords: number = 0;
  rowId: string;
  @ViewChild(PopUpBoxComponentRequestCert, {static:true}) popUpBox: PopUpBoxComponentRequestCert;
  pageSize = 10;
  page = 1;
  tableStructure = [
    {
      id: "certificateTicketNo",
      name: "Request No.",
      linkable: false,
    },
    {
      id: "organizationName",
      name: "Org. Name",
      linkable: false,
    },
    {
      id: "applicationName",
      name: "Application Name",
      linkable: false,
    },
    {
      id: "status",
      name: "Status",
      linkable: false,
    },
    {
      id: "expireDate",
      name: "Expire Date",
      linkable: false,
    },
    {
      id: "reason",
      name: "Reason",
      linkable: false,
      button: true,
      buttonName: "Details",
    },
    {
      id: "createdAt",
      name: "Created Date",
      linkable: false,
    },
  ];
  txtRequestNo: string;
  selectedAADGroup: string;
  isDisableAADGroup = true;
  aadGroupMasterLists = [];
  isLoadingAADGroup = true;
  certificates = [];
  showIndex = false;

  constructor(
    private certificateListService: CertificateService,
    private LoadingService: LoadingService,
    private labels: UITextForMigratingService,
    private userService: UserService,
  ) {}

  ngOnInit() {
    this.certificateListService.getAADGroup().subscribe((response) => {
      if (this.userService.userInfo.role === 'pm') {
        const aadGroupFilterBusinessUnit = response.data.filter(aadGroup => !!aadGroup.businessUnit)
        this.aadGroupMasterLists = aadGroupFilterBusinessUnit
        this.isDisableAADGroup = false;
      } else {
        this.aadGroupMasterLists = response.data.filter(data => data.businessUnit === this.userService.userInfo.businessUnit)
        this.selectedAADGroup = this.aadGroupMasterLists[0].businessUnit
        this.isDisableAADGroup = true;
      }
      
      this.getCertificate();
      this.isLoadingAADGroup = false
    }, (err) => {
      this.LoadingService.showError(this.labels.current.ERROR, err.error ? err.error.status.description : err.message);
    });
  }

  getRowId(row) {
    return row[this.rowId];
  }

  onClick(content, organizationName) {
    this.popUpBox.visible = false;
    this.popUpBox.content = content;
    this.popUpBox.organizationName = organizationName;
  }

  onPageChange() {
    this.getCertificate();
  }

  getCertificate() {
    if (this.userService.userInfo.role !== 'pm' && !this.selectedAADGroup) return;
    this.LoadingService.showLoading();
    this.certificateListService
      .getCertificateList((this.page - 1) * 10, 10, "createdAt", "DESC", this.txtRequestNo, this.selectedAADGroup)
      .subscribe(
        (res) => {
          this.LoadingService.hideLoading();
          // if (!res.success) {
          //   this.LoadingService.showError(
          //     this.labels.current.ERROR,
          //     res.message
          //   );
          //   return;
          // }
          this.totalRecords = res.data.totalRecord;
          this.certificates = res.data.certificateTickets;

        },
        (err) => {
          this.LoadingService.showError(this.labels.current.ERROR, err.error ? err.error.status.description : err.message);
        }
      );
  }
}
