import { Component, OnInit } from "@angular/core";
import { HttpClient } from "@angular/common/http";

@Component({
    selector: "app-analytics",
    templateUrl: "./analytics.component.html",
    styleUrls: ["./analytics.component.less"],
    standalone: false
})
export class AnalyticsComponent implements OnInit {
  activeUser: any;
  accessToken: any;

  constructor(private http: HttpClient) {}

  ngOnInit(): void {}
}
