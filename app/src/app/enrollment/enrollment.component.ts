import { Component, OnInit } from "@angular/core";
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";

import { UITextService } from "src/app/shared/services/uitext-service/uitext.service";
import { LoadingService } from "src/app/shared/services/loading-service/loading.service";
import { ValidatorService } from "src/app/shared/services/validator-service/validator.service";
import { EnrollmentEmailService } from "../enrollment/services/enrollment-email/enrollment-email.service"
import { PiiService } from "../services/pii/pii.service";
@Component({
    selector: "app-enroll",
    templateUrl: "./enrollment.component.html",
    styleUrls: ["./enrollment.component.less"],
    providers: [ValidatorService, EnrollmentEmailService],
    standalone: false
})
export class EnrollmentComponent implements OnInit {
  form: UntypedFormGroup;
  errorMessage: string;

  constructor(
    public labels: UITextService,
    private loadingService: LoadingService,
    private validator: ValidatorService,
    private formBuilder: UntypedFormBuilder,
    private enrollService: EnrollmentEmailService,
    private piiService:PiiService
  ) { 
      this.form = this.formBuilder.group({
      email: [
        "",
        [
          Validators.required,
          this.validator.isEmailValid,
          Validators.maxLength(255),
        ],
      ],
    });
  }

  ngOnInit() {
    this.errorMessage = null;
  }

  onChangeEvent(event: any){
    this.errorMessage = null;
  }

  onEnrollEmailClick() {
    if (this.form.invalid) {
      this.form.get("email").markAsTouched();
      return;
    }

    this.errorMessage = null;
    this.loadingService.showLoading();
    const postData = {
      parameter: `${this.form.controls.email.value}`,
      newValue: `${this.form.controls.email.value}`,
      recordKeyValue: `${this.form.controls.email.value}`,
      previousValue: ''
    }; 
    this.enrollService
      .enrollEmail(this.form.controls.email.value)
      .subscribe((response) => {
        this.loadingService.hideLoading();

        if (!response.success) {
          this.errorMessage = this.labels.current.GENERIC_SERVICE_SIDE_ERROR;
          return;
        }

        const code = response.data.status.code;

        if (code === 5131) {
          this.errorMessage = this.labels.current.EMAIL_ALREADY_VERIFIED;
          return;
        }

        if (code === 5132) {
          this.errorMessage = this.labels.current.EMAIL_VERIFY_IN_PROGRESS;
          return;
        }

        if (code === 1000) {
          this.piiService.postSensitive(postData,'Enroll Partner’s Email Address');
          this.loadingService
            .showSuccess(
              this.labels.current.ENROLL_EMAIL_SUCCESS_TITLE,
              this.labels.current.ENROLL_EMAIL_SUCCESS_MESSAGE
            )
            .then(() => {
              window.location.reload();
            });
        }
      });
  }

  onCheckEmailClick() {
    if (this.form.invalid) {
      this.form.get("email").markAsTouched();
      return;
    }

    this.errorMessage = null;
    this.loadingService.showLoading();
    this.enrollService
      .checkEmail(this.form.controls.email.value)
      .subscribe((response) => {
        this.loadingService.hideLoading();

        if (!response.success) {
          this.errorMessage = this.labels.current.GENERIC_SERVICE_SIDE_ERROR;
          return;
        }

        const code = response.data.status.code;
        const status = response.data.data.status;

        if (code === 1104) {
          this.errorMessage = this.labels.current.EMAIL_NOT_FOUND;
          return;
        }

        if (code !== 1000) {
          this.errorMessage = this.labels.current.GENERIC_SERVICE_SIDE_ERROR;
          return;
        }

        if (status === "verified") {
          this.errorMessage = this.labels.current.EMAIL_ALREADY_VERIFIED;
        } else if (
          [
            "pending",
            "pending can't enrolled email",
            "verify token expired",
          ].includes(status)
        ) {
          this.errorMessage = this.labels.current.EMAIL_VERIFY_IN_PROGRESS;
        } else if (status === "deleted") {
          this.errorMessage = this.labels.current.EMAIL_NOT_FOUND;
        } else if (status === "expired") {
          this.errorMessage = this.labels.current.EMAIL_EXPIRED;
        } else {
          this.errorMessage = this.labels.current.GENERIC_SERVICE_SIDE_ERROR;
        }
      });
  }

}
