import { Component, OnInit, Input } from "@angular/core";
import { LoadingService } from '../shared/services/loading-service/loading.service';
import { UITextForMigratingService } from "../services/uitext/uitext.service";
import { NgForm } from '@angular/forms';
import tableHeadersArray from "./apimanagement-table-headers.json";
import dayjs from "dayjs";
import { ApiConsumptionsManagementType, ApiManagementApiRequestType } from "./model/apiManagementType"
import { ApiConsumptionsMasterDataService } from "../services/apiconsumptions-masterdata/apiconsumptions-masterdata.service"
import { ServiceResponse } from '../models/service-response';
import { Router} from '@angular/router';

@Component({
    selector: "api-management",
    templateUrl: "./api-management.component.html",
    styleUrls: ["./api-management.component.less"],
    standalone: false
})
export class ApiManagementnComponent implements OnInit {
  // Table Related
  tableHeaders = tableHeadersArray;
  totalRecord = 0;
  pageSize = 20;
  page = 1;
  sizeOptions = [20, 40, 50];
  debounce: any;
  role: string;
  sortBy = "apiProduct";
  isAscOrdered = true;
  paginationLabel = "";

  public isMonthSelected:boolean = false

  apiManagementTableSearch: ApiConsumptionsManagementType[] = []
  apiManagementSliced: ApiConsumptionsManagementType[] = []
  groupedReportsFixed: ApiConsumptionsManagementType[] = []
  selectedMethod: string
  selectedPath: string
  scope: string
  selectedOAuth: boolean
  selectedTwoWaySsl: boolean
  selectedAppCode: boolean
  dropDownMethod = ["GET","POST","PUT","DELETE"];
  dropDownOAuth = [
    { label: '2 Legged', value: false },
    { label: '3 Legged', value: true }
  ];
  dropDowntwoWaySsl = [
    { label: 'Yes', value: true },
    { label: 'No', value: false }
  ];
  dropDownAppCode = [
    { label: 'Yes', value: true },
    { label: 'No', value: false }
  ];
  isSubmitted = false
  isTwoxxChecked = true
  isFourxxChecked = true
  isFivexxChecked = true

  keywordToFiltered = ""
  pathMasterLists: {
    apiEndpoint: string
  }[] = []

  // Loading
  isLoadingPath = true
  isLoadingOrgName = true
  isLoadingAppName = true

  constructor(
    public labels: UITextForMigratingService,
    private loading: LoadingService,
    private apiService: ApiConsumptionsMasterDataService,
    private router: Router
  ) { }

  getSubarrayReports(start: number, end: number) {
    return this.apiManagementTableSearch.slice(start, end);
  }

  ngOnInit() {
    this.apiService.getPaths().subscribe((response: ServiceResponse) => {
      this.pathMasterLists = response.data
      this.isLoadingPath = false
    }, (err) => {
      this.loading.showError(this.labels.current.ERROR, err.error ? err.error.status.description : err.message);
    });

    const sessionMngCon = sessionStorage.getItem("API_MNG_INPUT")
    if(sessionMngCon)
    {
      const requestApiMangement: ApiManagementApiRequestType = JSON.parse(sessionMngCon)

      this.selectedMethod = requestApiMangement.method
      this.selectedPath = requestApiMangement.path
      this.scope = requestApiMangement.scope
      this.selectedOAuth = requestApiMangement.oauth
      this.selectedTwoWaySsl = requestApiMangement.twoWaySSL
      this.selectedAppCode = requestApiMangement.applicationCode

      this.apiManagementAPI(requestApiMangement)

    
    }
  }

  ngAfterViewChecked() {
  }

  onSubmit(apiManagementForm: NgForm) {
    this.isSubmitted = true
    if (apiManagementForm.invalid) {
      return;
    }

    const twoWaySslSearch = apiManagementForm.form.controls.twoWaySslSearch.value === true || apiManagementForm.form.controls.twoWaySslSearch.value === false ?
    apiManagementForm.form.controls.twoWaySslSearch.value : undefined

    const appCodeSearch = apiManagementForm.form.controls.appCodeSearch.value === true || apiManagementForm.form.controls.appCodeSearch.value === false ?
    apiManagementForm.form.controls.appCodeSearch.value : undefined

    const oauthSearch = apiManagementForm.form.controls.oauthSearch.value === true || apiManagementForm.form.controls.oauthSearch.value === false ?
    apiManagementForm.form.controls.oauthSearch.value : undefined


    const requestPayload = {
      method: apiManagementForm.form.controls.methodSearch.value ? apiManagementForm.form.controls.methodSearch.value : undefined,
      path: apiManagementForm.form.controls.pathSearch.value ? apiManagementForm.form.controls.pathSearch.value : undefined,
      scope: apiManagementForm.form.controls.scope.value ? apiManagementForm.form.controls.scope.value : undefined,
      twoWaySSL: twoWaySslSearch,
      applicationCode: appCodeSearch,
      oauth: oauthSearch
    }

    this.apiManagementAPI(requestPayload)
    sessionStorage.setItem("API_MNG_INPUT",JSON.stringify(requestPayload))

    


  }

  apiManagementAPI(requestPayload: ApiManagementApiRequestType) {

    this.loading.showLoading();

    this.apiService.getApiManagement(requestPayload).subscribe((response:ServiceResponse) => {
      this.apiManagementTableSearch = response.data
      this.groupedReportsFixed = response.data;

      this.filterReport()
      
      this.loading.hideLoading();
      this.apiConsumptionTableSortingControl()
      this.apiConsumptionsShowControl()

    }, (err) => {
      if (err.status === 404 && err.error && err.error.status.code === 1104) {
        this.loading.hideLoading();
        
        this.totalRecord = 0;
        this.apiManagementSliced = [];
        this.apiManagementTableSearch = [];
        this.groupedReportsFixed = [];
        this.apiConsumptionTableSortingControl()
        this.apiConsumptionsShowControl()
      } else {
        this.loading.showError(this.labels.current.ERROR, err.error ? err.error.status.description : err.message);
      }
    }, () => {
      this.loading.hideLoading();
    })
  }

  onClear(apiManagementForm: NgForm)
  {
    apiManagementForm.form.reset()
  }

  managementDetail(apiManagement: ApiConsumptionsManagementType)
  {

    this.router.navigate(['apiInfo/management/detail'], {
      queryParams: apiManagement});
  }

  apiConsumptionTableSortingControl() {
    if (this.isAscOrdered) {
      this.apiManagementTableSearch = this.apiManagementTableSearch.sort((a, b) => {
        const valA = a[this.sortBy] || '';
        const valB = b[this.sortBy] || '';
        return valA.localeCompare(valB);
      });
    } else {
      this.apiManagementTableSearch = this.apiManagementTableSearch.sort((a, b) => {
        const valA = a[this.sortBy] || '';
        const valB = b[this.sortBy] || '';
        return valB.localeCompare(valA);
      });
    }
  }

  apiConsumptionsShowControl() {
    this.apiManagementSliced = this.getSubarrayReports((this.page - 1) * this.pageSize, this.pageSize * this.page )
    this.totalRecord = this.apiManagementTableSearch.length
    this.genPaginationLabel()
  }

  generateHeaderSortedId(field) {
    return field
      ? "task-list-header-" +
          field.toLowerCase().split(" ").join("-").replace(".", "")
      : "";
  }

  generatePageSizeOptionId(sizeOption) {
    return sizeOption ? "task-list-display-item-" + sizeOption : "";
  }

  sort(fieldName) {
    if (this.sortBy === fieldName) {
      this.isAscOrdered = !this.isAscOrdered;
    } else {
      this.isAscOrdered = true;
    }
    this.sortBy = fieldName;
    this.apiConsumptionTableSortingControl()
    this.apiConsumptionsShowControl()
  }

  shouldDisabled() {
    return this.totalRecord < this.sizeOptions[0];
  }

  onPageChange() {
    this.apiConsumptionsShowControl()
  }

  selectPageSize(sizeOption) {
    this.pageSize = sizeOption;

    this.apiConsumptionsShowControl()
  }

  genPaginationLabel() {
    let startIndex = this.pageSize * (this.page - 1) + 1;
    const lastIndex = startIndex + this.apiManagementSliced.length - 1;
    if (this.apiManagementSliced.length === 0) {
      startIndex = 0;
    }
    this.paginationLabel = `Showing ${startIndex}-${lastIndex} of ${this.totalRecord}`;
  }

  filterReport(){
    this.apiManagementTableSearch = this.groupedReportsFixed.filter(item => {
      return Object.values(item).some(value =>
        value.toString().toLowerCase().includes(this.keywordToFiltered.toLowerCase())
      );
    });
    this.apiConsumptionTableSortingControl()
    this.apiConsumptionsShowControl()
  }
}
