import { Component, OnInit, Input } from "@angular/core";
import { LoadingService } from "../../shared/services/loading-service/loading.service";
import { UITextForMigratingService } from "../../services/uitext/uitext.service";
import tableHeadersArray from "./api-management-detail-table-headers.json";
import { ApiManagementService } from "../../services/apimanagement/apimanagement.service";
import { ActivatedRoute, Router } from '@angular/router';

import { ServiceResponse } from '../../models/service-response';


import { OrgAppMasterType, ApiManagamentType, apiManagementCSVType } from './model/apiManagementType'

@Component({
    selector: "app-management-detail",
    templateUrl: "./api-management-detail.component.html",
    styleUrls: ["./api-management-detail.component.less"],
    standalone: false
})
export class ApiManagementComponentDetail implements OnInit {
  // Table Related

  tableHeaders = tableHeadersArray;
  totalRecord = 0;
  sortBy = "organizationName";
  isAscOrdered = true;
  paginationLabel = "";
  totalTwoxx: number = 0;
  totalFourxx: number = 0;
  totalFivexx: number = 0;
  totalSumOfApiCall: number = 0;

  appManagementList:OrgAppMasterType[] = []
  appManagementRaw:OrgAppMasterType[] = []
  apiFilterRaw:ApiManagamentType = {
    method: "",
    path: "",
    scope: "",
    twoWaySSL: "",
    apiProduct: "",
    oauthType: "",
    applicationCode: ""
  }



  constructor(
    public labels: UITextForMigratingService,
    private loading: LoadingService,
    private apiService: ApiManagementService,
    private router: Router, 
    private activatedRoute: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.loading.showLoading();
   
    this.activatedRoute.queryParams.subscribe( (inputParam:ApiManagamentType) => {
      this.apiFilterRaw = Object.assign({},inputParam)

    })


    const requestPayload = {
      path: this.apiFilterRaw.path,
      method: this.apiFilterRaw.method
    }

    this.apiService.postApiManagement(requestPayload).subscribe((response:ServiceResponse) => {

      this.appManagementRaw = response.data
      this.appManagementRaw = this.appManagementRaw.sort((a,b) => a.applicationName.localeCompare(b.applicationName))
      this.appManagementList = this.appManagementRaw.reduce((acc, curr) => {

        let found = acc.find(item => item.organizationName === curr.organizationName);
      
        if (found) {
          found.applicationName.push(curr.applicationName)
        } else {
          acc.push({
            organizationName: curr.organizationName,
            applicationName: [curr.applicationName]
          });
        }
      
        return acc;
      }, []);

      this.apiManagementTableSortingControl()

      this.loading.hideLoading();



    }, (err) => {
      if (err.status === 404 && err.error && err.error.status.code === 1104) {
        this.loading.hideLoading();
        this.appManagementList = []
      } else {
        this.loading.showError(this.labels.current.ERROR, err.error ? err.error.status.description : err.message);
      }
    }, () => {
      this.loading.hideLoading();
    })



  }


  private convertToCSV(data: any[]): string {
    const headers = Object.keys(data[0]) // Adjust headers as needed
    const csvRows = data.map(item => 
      headers.map(header => `"${item[header]}"`).join(',')
    );
    
    return [headers.join(','), ...csvRows].join('\n');
  }

  generateHeaderSortedId(field) {
    return field
      ? "task-list-header-" +
          field.toLowerCase().split(" ").join("-").replace(".", "")
      : "";
  }

  apiManagementTableSortingControl() {
    if (this.isAscOrdered) {
      this.appManagementList = this.appManagementList.sort((a, b) => {
        const valA = a[this.sortBy] || '';
        const valB = b[this.sortBy] || '';
        return valA.localeCompare(valB);
      });
      this.appManagementRaw = this.appManagementRaw.sort((a, b) => {
        const valA = a[this.sortBy] || '';
        const valB = b[this.sortBy] || '';
        return valA.localeCompare(valB);
      });
    } else {
      this.appManagementList = this.appManagementList.sort((a, b) => {
        const valA = a[this.sortBy] || '';
        const valB = b[this.sortBy] || '';
        return valB.localeCompare(valA);
      });
      this.appManagementRaw = this.appManagementRaw.sort((a, b) => {
        const valA = a[this.sortBy] || '';
        const valB = b[this.sortBy] || '';
        return valB.localeCompare(valA);
      });
    }
  }

  sort(fieldName) {
    if (this.sortBy === fieldName) {
      this.isAscOrdered = !this.isAscOrdered;
    } else {
      this.isAscOrdered = true;
    }

    this.sortBy = fieldName;
    this.apiManagementTableSortingControl()
  }
  exportToCSV(): void {
    this.loading.showLoading();
    
    let appManagementCsv:apiManagementCSVType[] = [
      {
        applicationId: "",
        applicationName: "",
        organizationId: "",
        organizationName: "",
        apiProduct: this.apiFilterRaw.apiProduct,
        method: this.apiFilterRaw.method,
        path: this.apiFilterRaw.path,
        scope: this.apiFilterRaw.scope,
        oauthType: this.apiFilterRaw.oauthType,
        twoWaySSL: this.apiFilterRaw.twoWaySSL,
        applicationCode: this.apiFilterRaw.applicationCode
      }
    ]
    if(this.appManagementRaw.length > 0)
    {
      appManagementCsv = this.appManagementRaw.map( mngItem => 
        {
          let appManageItem: OrgAppMasterType = Object.assign({}, mngItem)
          
          const appManageItemCsv = { ...appManageItem, 
            apiProduct: this.apiFilterRaw.apiProduct,
            method: this.apiFilterRaw.method,
            path: this.apiFilterRaw.path,
            scope: this.apiFilterRaw.scope,
            oauthType: this.apiFilterRaw.oauthType,
            twoWaySSL: this.apiFilterRaw.twoWaySSL,
            applicationCode: this.apiFilterRaw.applicationCode

          }
          
          return appManageItemCsv

        }
      )
    }
    
    const handleNull = (value: any) => value !== null && value !== undefined ? value : "";
    appManagementCsv.forEach(row => {
      row["Organization Name"] = handleNull(row.organizationName);
      row["Application Name"] = handleNull(row.applicationName);
      row["API Product"] = handleNull(row.apiProduct);
      row["Method"] = handleNull(row.method);
      row["Path"] = handleNull(row.path);
      row["Scope"] = handleNull(row.scope);
      row["Oauth 2.0"] = handleNull(row.oauthType);
      row["2-Way SSL"] = handleNull(row.twoWaySSL);
      row["Application Code"] = handleNull(row.applicationCode);
      delete row.organizationName;
      delete row.applicationName;
      delete row.organizationId;
      delete row.applicationId;
      delete row.apiProduct;
      delete row.method;
      delete row.path;
      delete row.scope;
      delete row.oauthType;
      delete row.twoWaySSL;
      delete row.applicationCode;
    });
    const csvData = this.convertToCSV(appManagementCsv);
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
  
    const a = document.createElement('a');
    a.href = url;
    const namefile = this.apiFilterRaw.path.replace("/","-")
    const methodfile = this.apiFilterRaw.method
    a.download = `results${namefile}_${methodfile}`;
    a.click();
    URL.revokeObjectURL(url);

    this.loading.hideLoading();
  }
}
