import { HttpClient } from "@angular/common/http";
import { Component, OnInit } from "@angular/core";
import dayjs from "dayjs";
import { LoadingService } from "src/app/shared/services/loading-service/loading.service";
import { UITextService } from "src/app/shared/services/uitext-service/uitext.service";
import { UserService } from "src/app/shared/services/user-service/user.service";
import { OrganizationService } from "../services/organization.service";
import tableHeadersArray from "./table-headers.json";
import { ActivatedRoute } from "@angular/router";
import { Token } from "../../shared/models/token";
import { Inject } from "@angular/core";
import { Router } from "@angular/router";


const ORGANIZATION_UUID = "ORGANIZATION_UUID";

@Component({
    selector: "app-application-list",
    templateUrl: "./application-list.component.html",
    styleUrls: ["./application-list.component.less"],
    standalone: false
})
export class ApplicationListComponent implements OnInit {
  tableHeaders = tableHeadersArray;
  totalRecord = 0;
  applications = [];
  organizationUuid = "";
  pageSize = 10;
  page = 1;
  sizeOptions = [10, 20, 50, 100];
  debounce: any;
  sortBy = "updatedDate";
  isAscOrdered = false;
  paginationLabel = "";
  token: Token;

  constructor(
    private organizationService: OrganizationService,
    private userService: UserService,
    private loading: LoadingService,
    public labels: UITextService,
    private httpClient: HttpClient,
    private route: ActivatedRoute,
    private router: Router, 
  ) {
    this.token = JSON.parse(localStorage.getItem(ORGANIZATION_UUID));
  }

  ngOnInit() {
    this.userService.getUserInfo().subscribe((userInfo) => {
      if (userInfo) {
        this.getApplicationsByOrgUuid();
      }
    });
  }

  formatTime(timeInMilli) {
    return dayjs.unix(timeInMilli).format("DD/MM/YYYY");
  }

  debounceGetAppByOrgUuid() {
    clearTimeout(this.debounce);
    this.debounce = setTimeout(() => {
      this.getApplicationsByOrgUuid();
    }, 100);
  }

  onPageChange() {
    this.debounceGetAppByOrgUuid();
  }

  sort(fieldName) {
    if (this.sortBy === fieldName) {
      this.isAscOrdered = !this.isAscOrdered;
    } else {
      this.isAscOrdered = true;
    }

    this.sortBy = fieldName;
    this.debounceGetAppByOrgUuid();
  }

  selectPageSize(sizeOption) {
    this.pageSize = sizeOption;
    this.debounceGetAppByOrgUuid();
  }

  shouldDisabled() {
    return this.totalRecord < this.sizeOptions[0];
  }

  generateHeaderSortedId(field) {
    return field
      ? "task-list-header-" +
          field.toLowerCase().split(" ").join("-").replace(".", "")
      : "";
  }

  generatePageSizeOptionId(sizeOption) {
    return sizeOption ? "task-list-display-item-" + sizeOption : "";
  }

  getApplicationsByOrgUuid() {
    this.route.paramMap.subscribe((params) => {
      if (params.get("organizationUuid") == ":organizationUuid"){
        this.organizationUuid = localStorage.getItem(ORGANIZATION_UUID);
        this.router.navigate([`organization/${this.organizationUuid}/application`]);
        return true;
      }else{
        this.organizationUuid = params.get("organizationUuid");
      }
    });

    if (this.organizationUuid) {
      localStorage.setItem(ORGANIZATION_UUID, this.organizationUuid);
    }

    this.loading.showLoading();
    this.organizationService
      .getApplicationsUnderOrganization(this.organizationUuid)
      .subscribe(
        (result) => {
          const { totalRecord, applications } = result.data;
          this.loading.hideLoading();
          if (result.data) {
            this.totalRecord = totalRecord;
            this.applications = applications;
          }
        },
        (err) => {
          this.loading.hideLoading();
          this.loading.showError(
            this.labels.current.TASK_LIST_ERROR_TITLE,
            this.labels.current.TASK_LIST_ERROR_DETAIL
          );
        }
      );
  }
}
