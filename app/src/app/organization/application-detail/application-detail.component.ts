import { Component, OnInit, ViewChild } from "@angular/core";
import { LoadingService } from "src/app/shared/services/loading-service/loading.service";
import { UITextService } from "src/app/shared/services/uitext-service/uitext.service";
import { Router, ActivatedRoute } from "@angular/router";
import { OnboardingService } from "../services/onboarding.service";
import { ApiScopeService } from "../services/api-scope.service";
import * as _ from "lodash";
import { ActionResponse } from "../../models/action-response";
import { get } from "lodash";
import { Eform } from "../services/onboarding-e-form/eform";
import { EFormDataMapper } from "../services/onboarding-e-form/eformDataMapper";
import { PiiService } from "../../services/pii/pii.service";

@Component({
    selector: "app-application-detail",
    templateUrl: "./application-detail.component.html",
    styleUrls: ["./application-detail.component.less"],
    standalone: false
})
export class ApplicationDetailComponent implements OnInit {
  subtaskUuid: string;
  generalInfoValue: any = {};
  scopeObject: any = {};
  certificateValues: any;
  authenticationValues: any;

  constructor(
    public labels: UITextService,
    private loading: LoadingService,
    private router: Router,
    private route: ActivatedRoute,
    private onboardingService: OnboardingService,
    private scopeService: ApiScopeService,
    private eformMapper: EFormDataMapper,
    private piiService:PiiService
  ) {}

  ngOnInit() {
    this.loadInitForm();
  }

  loadInitForm() {
    this.subtaskUuid = this.route.snapshot.params.subtaskUuid;
    this.loadApplicationDetail();
  }

  loadApplicationDetail() {
    this.loading.showLoading();
    this.scopeService.getAvailableScopes().subscribe((scopes) => {
      this.onboardingService
        .getSubtaskByUuid(this.subtaskUuid)
        .subscribe((response: ActionResponse) => {
          const data = response.data;
          const applicationUrl = get(
            data,
            "environment_section.applicationUrl"
          );
          const mappedData = {
            ...data,
            ...data.general_section,
            ...data.scope_service_section,
            ...data.environment_section,
            applicationUrl: applicationUrl === "null" ? null : applicationUrl,
            engCorporateName: data.general_section.corporateNameEn,
            thaiCorporateName: data.general_section.corporateNameTh,
            testingFileFromUAT: data.general_section.testFileUrl,
            applicationName: data.general_section.applicationName,
            engApplicationDisplayName: data.general_section.applicationNameEn,
            thaiApplicationDisplayName: data.general_section.applicationNameTh,
            mobileNo: data.general_section.mobileNumber,
            goLiveDate: data.development_section.goLiveDate,
            developmentPeriodFrom: data.development_section.periodFrom,
            developmentPeriodTo: data.development_section.periodTo,
            testingPeriodFrom: data.development_section.testingFrom,
            testingPeriodTo: data.development_section.testingTo,
            taskState: data.subTaskStateName,
          };
          const {
            certificate_section,
            authenticationType,
            applicationCodeNss,
            isRequireConsent,
            productNameNss,
          } = mappedData;
          const result = this.mapToFormValues(mappedData);
          this.generalInfoValue = {
            ...result,
            industryType: mappedData.industryType,
          };
          const postData = {
            parameter: `${this.generalInfoValue.taxId} ${this.generalInfoValue.email} ${this.generalInfoValue.contactPerson} ${this.generalInfoValue.mobileNo}`,
            newValue: '',
            recordKeyValue: '',
            previousValue: ''
          }; 
          this.piiService.postSensitive(postData,'View Application Info');
          this.setCertificateValues(certificate_section);
          this.setAuthenticationValues({
            authenticationType,
            applicationCodeNss,
            isRequireConsent,
            productNameNss,
          });
          this.scopeObject = this.mapScopes(scopes.data, mappedData.scopes.id);
          this.loading.hideLoading();
        });
    });
  }

  mapToFormValues(dto): Eform {
    return this.eformMapper.toDataValue(dto);
  }

  setCertificateValues(cerValue) {
    this.certificateValues = {
      ...cerValue,
      csrName:
        cerValue.certificateType === "AUTO"
          ? ""
          : this.setCertificateName(cerValue.csrName || cerValue.csrFile),
    };
  }

  setCertificateName(csrFullName: any): string {
    const path = csrFullName.split("/");
    if (path.length === 1) {
      return csrFullName;
    } else {
      const csrName = path[6].split("?");
      return csrName[0];
    }
  }

  setAuthenticationValues(authValue) {
    this.authenticationValues = {
      authenticationType: JSON.parse(authValue.authenticationType),
      applicationCodeNss: authValue.applicationCodeNss,
      isRequireConsent: authValue.isRequireConsent,
    };
    if (this.authenticationValues.authenticationType.includes("web-authen")) {
      this.authenticationValues.productNameNss = JSON.parse(
        authValue.productNameNss
      );
    } else {
      this.authenticationValues.productNameNss = [null];
    }
  }

  // TODO(Thanwarin): Refactor of storing stage of scopes
  mapScopes(data, scopeId) {
    _.each(data, (scope) => {
      scope.selected = scopeId.includes(scope.scopeId) ? true : false;
      const newData = _.chain(scope.subScopes)
        .groupBy("groupName")
        .map((value, key) => {
          const extendedSubscope = _.map(value, (v) => ({
            ...v,
            selected: scopeId.includes(v.subScopeId) ? true : false,
          }));
          return {
            groupName: key,
            subscopes: extendedSubscope,
          };
        })
        .value();
      scope.groupedSubscopes = newData;
    });
    return data;
  }
}
