import { HttpClient } from "@angular/common/http";
import { Component, OnInit } from "@angular/core";
import dayjs from "dayjs";
import { LoadingService } from "src/app/shared/services/loading-service/loading.service";
import { UITextService } from "src/app/shared/services/uitext-service/uitext.service";
import { UserService } from "src/app/shared/services/user-service/user.service";
import { OrganizationService } from "../services/organization.service";
import tableHeadersArray from "./table-headers.json";

@Component({
    selector: "app-organization-list",
    templateUrl: "./organization-list.component.html",
    styleUrls: ["./organization-list.component.less"],
    standalone: false
})
export class OrganizationListComponent implements OnInit {
  tableHeaders = tableHeadersArray;
  totalRecord = 0;
  organizations = [];
  pageSize = 10;
  page = 1;
  sizeOptions = [10, 20, 50, 100];
  debounce: any;
  sortBy = "updatedDate";
  isAscOrdered = false;
  paginationLabel = "";

  constructor(
    private organizationService: OrganizationService,
    private userService: UserService,
    private loading: LoadingService,
    public labels: UITextService,
    private httpClient: HttpClient
  ) {}

  ngOnInit() {
    this.userService.getUserInfo().subscribe((userInfo) => {
      if (userInfo) {
        this.getOrganizations();
      }
    });
  }

  formatTime(timeInMilli) {
    return dayjs.unix(timeInMilli).format("DD/MM/YYYY");
  }

  debounceGetOrganizations() {
    clearTimeout(this.debounce);
    this.debounce = setTimeout(() => {
      this.getOrganizations();
    }, 100);
  }

  onPageChange() {
    this.debounceGetOrganizations();
  }

  sort(fieldName) {
    if (this.sortBy === fieldName) {
      this.isAscOrdered = !this.isAscOrdered;
    } else {
      this.isAscOrdered = true;
    }

    this.sortBy = fieldName;
    this.debounceGetOrganizations();
  }

  selectPageSize(sizeOption) {
    this.pageSize = sizeOption;
    this.debounceGetOrganizations();
  }

  shouldDisabled() {
    return this.totalRecord < this.sizeOptions[0];
  }

  generateHeaderSortedId(field) {
    return field
      ? "task-list-header-" +
          field.toLowerCase().split(" ").join("-").replace(".", "")
      : "";
  }

  generatePageSizeOptionId(sizeOption) {
    return sizeOption ? "task-list-display-item-" + sizeOption : "";
  }

  getOrganizations() {
    this.loading.showLoading();
    this.organizationService.getOrganizationList().subscribe(
      (result) => {
        this.loading.hideLoading();
        if (result.data) {
          this.totalRecord = result.data.length;
          this.organizations = result.data;
        }
      },
      (err) => {
        this.loading.hideLoading();
        this.loading.showError(
          this.labels.current.TASK_LIST_ERROR_TITLE,
          this.labels.current.TASK_LIST_ERROR_DETAIL
        );
      }
    );
  }
}
