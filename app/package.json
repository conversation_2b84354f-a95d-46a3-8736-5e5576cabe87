{"name": "scb-partner-frontend-adminportal-app", "version": "1.9.1", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --prod", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^19.2.8", "@angular/common": "^19.2.8", "@angular/compiler": "^19.2.8", "@angular/core": "^19.2.8", "@angular/forms": "^19.2.8", "@angular/platform-browser": "^19.2.8", "@angular/platform-browser-dynamic": "^19.2.8", "@angular/router": "^19.2.8", "@azure/msal-angular": "^4.0.14", "@azure/msal-browser": "^2.38.3", "@azure/msal-node": "^2.6.4", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "@ng-select/ng-select": "^14.0.0", "@ngx-pwa/local-storage": "^20.0.0", "@popperjs/core": "^2.11.8", "angulartics2": "^14.1.0", "axios": "^1.6.7", "bootstrap": "^4.6.1", "dayjs": "^1.11.13", "jsonpath": "^1.1.1", "jwt-decode": "^4.0.0", "luxon": "^3.4.4", "ngx-cookie-service": "^2.2.0", "numeral": "^2.0.6", "rxjs": "~6.6.7", "tslib": "^2.0.0", "validate-azure-ad-token": "^2.2.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.9", "@angular-devkit/schematics": "^19.2.9", "@angular/cli": "^19.2.9", "@angular/compiler-cli": "^19.2.8", "@angular/language-service": "^19.2.8", "@types/jasmine": "~3.3.8", "@types/jasminewd2": "^2.0.10", "@types/jsonpath": "^0.2.4", "@types/luxon": "^3.4.2", "@types/node": "^12.11.1", "@types/numeral": "^2.0.5", "codelyzer": "^5.1.2", "jasmine-spec-reporter": "~5.0.0", "protractor": "~7.0.0", "ts-node": "~7.0.0", "tslint": "~6.1.0", "typescript": "~5.8.3"}}