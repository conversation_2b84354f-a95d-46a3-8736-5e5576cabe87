{"compileOnSave": false, "compilerOptions": {"skipLibCheck": true, "baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "experimentalDecorators": true, "module": "es2020", "moduleResolution": "node", "resolveJsonModule": true, "esModuleInterop": true, "importHelpers": true, "target": "ES2022", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "useDefineForClassFields": false}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true}}